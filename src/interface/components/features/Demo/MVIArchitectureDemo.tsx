/**
 * MVIArchitectureDemo - Demo component to test 3-layer MVI architecture
 */

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Model Layer imports
import { USER_STATUS, CREDIT_TRANSACTION_TYPES } from '@/models/constants';
import { User, StatsData } from '@/models/types';

// Data Layer imports
import { userRepository, analyticsRepository } from '@/data/repositories';
import { mockUsers, mockStatsData } from '@/data/sources/mockDataSource';

// Interface Layer imports
import { 
  useUsers, 
  useDashboardStats, 
  useUserHelpers 
} from '@/interface/hooks/data';
import { usePagination } from '@/interface/hooks/ui';
import { 
  formatNumber, 
  formatDate, 
  formatRelativeTime 
} from '@/interface/utils/formatters';
import { validateUser } from '@/interface/utils/validation';

const MVIArchitectureDemo: React.FC = () => {
  // Data hooks - Interface Layer → Data Layer
  const pagination = usePagination({ initialPageSize: 5 });
  const { data: usersData, isLoading } = useUsers(pagination.paginationParams);
  const { statsData, isLoading: isLoadingStats } = useDashboardStats();
  const userHelpers = useUserHelpers();

  // Test functions
  const testModelLayer = () => {
    console.log('=== MODEL LAYER TEST ===');
    console.log('User Status Constants:', USER_STATUS);
    console.log('Credit Transaction Types:', CREDIT_TRANSACTION_TYPES);
    
    // Test type validation
    try {
      const testUser = validateUser(mockUsers[0]);
      console.log('✅ User validation passed:', testUser.name);
    } catch (error) {
      console.log('❌ User validation failed:', error);
    }
  };

  const testDataLayer = async () => {
    console.log('=== DATA LAYER TEST ===');
    
    // Test repository
    const userSummary = await userRepository.getUserSummary();
    console.log('✅ User Repository:', userSummary);
    
    const dashboardStats = await analyticsRepository.getDashboardStats();
    console.log('✅ Analytics Repository:', dashboardStats);
    
    // Test helper functions
    const successRate = userRepository.calculateSuccessRate(mockUsers[0]);
    console.log('✅ Business Logic (Success Rate):', successRate);
  };

  const testInterfaceLayer = () => {
    console.log('=== INTERFACE LAYER TEST ===');
    
    // Test formatters
    console.log('✅ Number Formatter:', formatNumber(12450));
    console.log('✅ Date Formatter:', formatDate(new Date()));
    console.log('✅ Relative Time:', formatRelativeTime('2024-01-15 14:30'));
    
    // Test helpers
    console.log('✅ User Initials:', userHelpers.getUserInitials('Nguyễn Văn A'));
    console.log('✅ Status Info:', userHelpers.getStatusInfo('active'));
  };

  return (
    <div className="spacing-responsive max-w-4xl mx-auto">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-6">3-Layer MVI Architecture Demo</h1>
        
        {/* Test Buttons */}
        <div className="flex flex-wrap gap-4 mb-6">
          <Button onClick={testModelLayer} variant="outline">
            Test Model Layer
          </Button>
          <Button onClick={testDataLayer} variant="outline">
            Test Data Layer
          </Button>
          <Button onClick={testInterfaceLayer} variant="outline">
            Test Interface Layer
          </Button>
        </div>

        {/* Architecture Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="p-4 border-blue-200 bg-blue-50">
            <h3 className="font-semibold text-blue-900 mb-2">Model Layer</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>✅ Pure Types & Interfaces</li>
              <li>✅ Business Constants</li>
              <li>✅ Enums & Status</li>
              <li>✅ No Dependencies</li>
            </ul>
          </Card>

          <Card className="p-4 border-green-200 bg-green-50">
            <h3 className="font-semibold text-green-900 mb-2">Data Layer</h3>
            <ul className="text-sm text-green-800 space-y-1">
              <li>✅ Repositories</li>
              <li>✅ Business Logic</li>
              <li>✅ Mock Data Sources</li>
              <li>✅ API Integration</li>
            </ul>
          </Card>

          <Card className="p-4 border-purple-200 bg-purple-50">
            <h3 className="font-semibold text-purple-900 mb-2">Interface Layer</h3>
            <ul className="text-sm text-purple-800 space-y-1">
              <li>✅ React Components</li>
              <li>✅ Data Hooks</li>
              <li>✅ UI State Management</li>
              <li>✅ Zod Validation</li>
            </ul>
          </Card>
        </div>

        {/* Live Data Demo */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Live Data from Repositories</h2>
          
          {/* Dashboard Stats */}
          <Card className="p-4">
            <h3 className="font-medium mb-3">Dashboard Statistics</h3>
            {isLoadingStats ? (
              <p>Loading stats...</p>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                {statsData.map((stat, index) => (
                  <div key={index} className="text-center">
                    <p className="text-sm text-slate-600">{stat.title}</p>
                    <p className="text-lg font-bold">{stat.value}</p>
                    <Badge variant="secondary" className="text-xs">
                      {stat.change}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </Card>

          {/* Users Data */}
          <Card className="p-4">
            <h3 className="font-medium mb-3">Users from Repository</h3>
            {isLoading ? (
              <p>Loading users...</p>
            ) : (
              <div className="space-y-3">
                {usersData?.items.slice(0, 3).map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-3 bg-slate-50 rounded">
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-slate-600">{user.email}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatNumber(user.credits)} credits</p>
                      <Badge className={userHelpers.getStatusInfo(user.status).color}>
                        {userHelpers.getStatusInfo(user.status).label}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>

          {/* Dependency Flow */}
          <Card className="p-4">
            <h3 className="font-medium mb-3">Dependency Flow</h3>
            <div className="text-sm space-y-2">
              <p><strong>✅ Correct:</strong> Interface Layer → Data Layer → Model Layer</p>
              <p><strong>✅ Correct:</strong> Components → Data Hooks → Repositories → Types</p>
              <p><strong>❌ Forbidden:</strong> Model Layer → Data Layer</p>
              <p><strong>❌ Forbidden:</strong> Data Layer → Interface Layer</p>
              <p><strong>❌ Forbidden:</strong> Components → Repositories (must go through hooks)</p>
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
};

export default MVIArchitectureDemo;
