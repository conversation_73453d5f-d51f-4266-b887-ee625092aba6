/**
 * Credit system types for transaction management
 */

export interface CreditTransaction {
  id: string;
  userId: number;
  type: CreditTransactionType;
  amount: number;
  balance: number;
  description: string;
  timestamp: string;
  adminId?: number;
  adminName?: string;
  status: TransactionStatus;
  metadata?: {
    orderId?: string;
    paymentMethod?: string;
    reason?: string;
  };
}

export type CreditTransactionType = 'add' | 'subtract' | 'used' | 'refund' | 'bonus';

export type TransactionStatus = 'pending' | 'completed' | 'failed' | 'cancelled';

export interface CreditBalance {
  userId: number;
  currentBalance: number;
  totalAdded: number;
  totalUsed: number;
  totalRefunded: number;
  lastUpdated: string;
}

export interface CreditUsage {
  userId: number;
  contentType: 'video' | 'photo';
  creditsUsed: number;
  contentId: string;
  timestamp: string;
  status: 'success' | 'failed';
  errorMessage?: string;
}

export interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  currency: string;
  description: string;
  isActive: boolean;
  features: string[];
  validityDays?: number;
}

export interface CreditSummary {
  totalCreditsInSystem: number;
  totalCreditsIssued: number;
  totalCreditsUsed: number;
  totalCreditsRemaining: number;
  averageCreditsPerUser: number;
  topUsers: {
    userId: number;
    userName: string;
    creditsUsed: number;
  }[];
}

export interface CreditHistory {
  transactions: CreditTransaction[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
  hasNextPage: boolean;
}
