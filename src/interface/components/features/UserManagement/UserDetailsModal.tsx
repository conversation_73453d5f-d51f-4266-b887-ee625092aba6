/**
 * UserDetailsModal - Modal for user details and editing
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { ModalState } from '@/models/types';

interface UserDetailsModalProps {
  modal: ModalState;
  onClose: () => void;
}

export const UserDetailsModal: React.FC<UserDetailsModalProps> = ({
  modal,
  onClose
}) => {
  return (
    <Dialog open={modal.isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>{modal.title}</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-slate-600">
            Tính năng tạo/chỉnh sửa người dùng sẽ được triển khai trong phase tiếp theo.
          </p>
        </div>

        <DialogFooter>
          <Button onClick={onClose}>
            Đóng
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
