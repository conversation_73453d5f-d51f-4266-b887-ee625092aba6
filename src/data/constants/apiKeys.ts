/**
 * API keys, cache keys, and storage keys
 */

export const STORAGE_KEYS = {
  AUTH_TOKEN: 'mega_ai_auth_token',
  REFRESH_TOKEN: 'mega_ai_refresh_token',
  USER_PREFERENCES: 'mega_ai_user_preferences',
  SIDEBAR_COLLAPSED: 'mega_ai_sidebar_collapsed',
  THEME: 'mega_ai_theme',
  LANGUAGE: 'mega_ai_language'
} as const;

export const CACHE_KEYS = {
  // User cache keys
  USERS_LIST: 'users_list',
  USER_DETAILS: (id: number) => `user_details_${id}`,
  USER_STATS: 'user_stats',
  USER_ACTIVITY: (id: number) => `user_activity_${id}`,

  // Credit cache keys
  CREDIT_TRANSACTIONS: 'credit_transactions',
  CREDIT_HISTORY: (userId: number) => `credit_history_${userId}`,
  CREDIT_SUMMARY: 'credit_summary',

  // Content cache keys
  CONTENT_LIST: 'content_list',
  CONTENT_DETAILS: (id: string) => `content_details_${id}`,
  CONTENT_STATS: 'content_stats',

  // Analytics cache keys
  DASHBOARD_STATS: 'dashboard_stats',
  ANALYTICS_USERS: 'analytics_users',
  ANALYTICS_CONTENT: 'analytics_content',
  ANALYTICS_CREDITS: 'analytics_credits',
  GROWTH_CHART: 'growth_chart',

  // Notification cache keys
  NOTIFICATIONS_LIST: 'notifications_list',
  NOTIFICATION_TEMPLATES: 'notification_templates',

  // System cache keys
  SYSTEM_SETTINGS: 'system_settings',
  SYSTEM_HEALTH: 'system_health',
  FEATURE_FLAGS: 'feature_flags',
  AUDIT_LOGS: 'audit_logs'
} as const;

export const QUERY_STALE_TIME = {
  IMMEDIATE: 0,
  SHORT: 1 * 60 * 1000,      // 1 minute
  MEDIUM: 5 * 60 * 1000,     // 5 minutes
  LONG: 15 * 60 * 1000,      // 15 minutes
  VERY_LONG: 60 * 60 * 1000  // 1 hour
} as const;

export const QUERY_CACHE_TIME = {
  SHORT: 5 * 60 * 1000,      // 5 minutes
  MEDIUM: 15 * 60 * 1000,    // 15 minutes
  LONG: 30 * 60 * 1000,      // 30 minutes
  VERY_LONG: 60 * 60 * 1000  // 1 hour
} as const;
