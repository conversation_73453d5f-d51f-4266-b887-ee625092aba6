/**
 * Asset Management - Central export for all assets with proper path constants
 */

// Asset base paths
export const ASSET_PATHS = {
  IMAGES: '/src/interface/assets/images',
  ICONS: '/src/interface/assets/icons',
  FONTS: '/src/interface/assets/fonts',
  STYLES: '/src/interface/assets/styles'
} as const;

// Public asset paths (for assets that need to be in public folder)
export const PUBLIC_PATHS = {
  FAVICON: '/favicon.ico',
  PLACEHOLDER: '/placeholder.svg',
  ROBOTS: '/robots.txt'
} as const;

// Image assets
export const IMAGES = {
  LOGO: `${ASSET_PATHS.IMAGES}/logo.svg`,
  LOGO_DARK: `${ASSET_PATHS.IMAGES}/logo-dark.svg`,
  AVATAR_PLACEHOLDER: `${ASSET_PATHS.IMAGES}/avatar-placeholder.png`,
  EMPTY_STATE: `${ASSET_PATHS.IMAGES}/empty-state.svg`,
  ERROR_STATE: `${ASSET_PATHS.IMAGES}/error-state.svg`,
  LOADING_SPINNER: `${ASSET_PATHS.IMAGES}/loading-spinner.svg`
} as const;

// Icon assets (custom SVG icons)
export const CUSTOM_ICONS = {
  MEGA_AI: `${ASSET_PATHS.ICONS}/mega-ai.svg`,
  DASHBOARD: `${ASSET_PATHS.ICONS}/dashboard.svg`,
  USERS: `${ASSET_PATHS.ICONS}/users.svg`,
  CREDITS: `${ASSET_PATHS.ICONS}/credits.svg`,
  CONTENT: `${ASSET_PATHS.ICONS}/content.svg`,
  ANALYTICS: `${ASSET_PATHS.ICONS}/analytics.svg`,
  SETTINGS: `${ASSET_PATHS.ICONS}/settings.svg`
} as const;

// Font assets
export const FONTS = {
  PRIMARY: `${ASSET_PATHS.FONTS}/inter.woff2`,
  SECONDARY: `${ASSET_PATHS.FONTS}/roboto.woff2`,
  MONO: `${ASSET_PATHS.FONTS}/fira-code.woff2`
} as const;

// Style assets
export const STYLES = {
  COMPONENTS: `${ASSET_PATHS.STYLES}/components.css`,
  UTILITIES: `${ASSET_PATHS.STYLES}/utilities.css`,
  ANIMATIONS: `${ASSET_PATHS.STYLES}/animations.css`,
  RESPONSIVE: `${ASSET_PATHS.STYLES}/responsive.css`
} as const;

// Asset helper functions
export const getAssetUrl = (path: string): string => {
  // Handle different environments
  if (import.meta.env.DEV) {
    return path;
  }
  
  // Production asset handling
  return new URL(path, import.meta.url).href;
};

export const getImageUrl = (imageName: keyof typeof IMAGES): string => {
  return getAssetUrl(IMAGES[imageName]);
};

export const getIconUrl = (iconName: keyof typeof CUSTOM_ICONS): string => {
  return getAssetUrl(CUSTOM_ICONS[iconName]);
};

export const getPublicAssetUrl = (assetName: keyof typeof PUBLIC_PATHS): string => {
  return PUBLIC_PATHS[assetName];
};

// Asset preloading utilities
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

export const preloadImages = async (srcs: string[]): Promise<void> => {
  await Promise.all(srcs.map(preloadImage));
};

// Asset validation utilities
export const isValidImageUrl = (url: string): boolean => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'];
  return imageExtensions.some(ext => url.toLowerCase().endsWith(ext));
};

export const getImageDimensions = (src: string): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.onerror = reject;
    img.src = src;
  });
};
