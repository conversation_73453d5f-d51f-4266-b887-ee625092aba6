/**
 * Notification system types
 */

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  status: NotificationStatus;
  priority: NotificationPriority;
  targetUsers: NotificationTarget;
  createdAt: string;
  scheduledAt?: string;
  sentAt?: string;
  createdBy: {
    id: number;
    name: string;
  };
  metadata?: NotificationMetadata;
  deliveryStats?: DeliveryStats;
}

export type NotificationType = 
  | 'system' 
  | 'credit' 
  | 'content' 
  | 'promotion' 
  | 'maintenance' 
  | 'security';

export type NotificationStatus = 
  | 'draft' 
  | 'scheduled' 
  | 'sending' 
  | 'sent' 
  | 'failed' 
  | 'cancelled';

export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface NotificationTarget {
  type: 'all' | 'specific' | 'filtered';
  userIds?: number[];
  filters?: {
    status?: string[];
    joinDateRange?: {
      from: string;
      to: string;
    };
    creditRange?: {
      min: number;
      max: number;
    };
  };
}

export interface NotificationMetadata {
  channels: DeliveryChannel[];
  template?: string;
  variables?: Record<string, any>;
  actionUrl?: string;
  actionLabel?: string;
  expiresAt?: string;
}

export type DeliveryChannel = 'in-app' | 'email' | 'sms' | 'push';

export interface DeliveryStats {
  totalTargets: number;
  delivered: number;
  failed: number;
  opened: number;
  clicked: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: NotificationType;
  subject: string;
  content: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserNotification {
  id: string;
  notificationId: string;
  userId: number;
  status: 'unread' | 'read' | 'archived';
  deliveredAt: string;
  readAt?: string;
  clickedAt?: string;
}
