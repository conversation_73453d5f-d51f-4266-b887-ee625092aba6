/**
 * DashboardContainer - New dashboard using 3-layer MVI architecture
 * Replaces Dashboard.tsx with proper separation of concerns
 */

import React from 'react';
import { Card } from '@/components/ui/card';

// Data hooks - Repository integration
import { useRealTimeDashboard, useUserGrowthChart } from '@/interface/hooks/data';

// Components
import { DashboardStats } from './DashboardStats';
import { QuickStatsRow } from './QuickStatsRow';
import { UserGrowthChart } from './UserGrowthChart';
import { LoadingDashboard } from './LoadingDashboard';
import { ErrorDashboard } from './ErrorDashboard';

const DashboardContainer: React.FC = () => {
  // Real-time dashboard data
  const {
    statsData,
    quickStats,
    isLoading,
    hasError,
    statsError,
    refetchStats
  } = useRealTimeDashboard();

  // Growth chart data
  const {
    data: growthChartData,
    isLoading: isLoadingChart,
    error: chartError
  } = useUserGrowthChart('month');

  // Error state
  if (hasError) {
    return (
      <ErrorDashboard 
        error={statsError?.message || 'Có lỗi xảy ra khi tải dữ liệu dashboard'}
        onRetry={refetchStats}
      />
    );
  }

  // Loading state
  if (isLoading) {
    return <LoadingDashboard />;
  }

  return (
    <div className="spacing-responsive">
      {/* Enhanced Stats Grid */}
      <DashboardStats 
        statsData={statsData}
        isLoading={isLoading}
      />

      {/* Quick Stats Row */}
      <QuickStatsRow 
        quickStats={quickStats}
        isLoading={isLoading}
      />

      {/* Charts */}
      <UserGrowthChart 
        data={growthChartData}
        isLoading={isLoadingChart}
        error={chartError?.message}
      />
    </div>
  );
};

export default DashboardContainer;
