/**
 * User entity types for Mega AI Admin
 * Extracted from UserCreditManagement.tsx data structures
 */

export interface MonthlyStats {
  videosCompleted: number;
  videosErrors: number;
  photosCompleted: number;
  photosErrors: number;
  creditsUsed: number;
  creditsAdded: number;
}

export interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  credits: number;
  videosCreated: number;
  photosCreated: number;
  totalUsed: number;
  totalAdded: number;
  monthlyStats: MonthlyStats;
  lastActivity: string;
  joinDate: string;
  status: UserStatus;
}

export type UserStatus = 'active' | 'premium' | 'inactive';

export interface UserFilters {
  status?: UserStatus;
  searchTerm?: string;
  dateRange?: {
    from: string;
    to: string;
  };
}

export interface UserSummary {
  totalUsers: number;
  activeUsers: number;
  premiumUsers: number;
  totalCredits: number;
  totalCreditsAdded: number;
  totalCreditsUsed: number;
  totalVideos: number;
  totalPhotos: number;
}

export interface UserActivityLog {
  id: number;
  userId: number;
  action: string;
  details: string;
  timestamp: string;
  ipAddress?: string;
}
