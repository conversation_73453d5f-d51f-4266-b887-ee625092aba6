/**
 * LoadingDashboard - Loading state for dashboard
 */

import React from 'react';
import { Card } from '@/components/ui/card';

const LoadingDashboard: React.FC = () => {
  return (
    <div className="spacing-responsive">
      {/* Stats Grid Loading */}
      <div className="grid-responsive-1-2-3 gap-4 sm:gap-6">
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="p-6 animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-6 bg-gray-200 rounded"></div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Quick Stats Loading */}
      <div className="grid-responsive-1-2-3 gap-4">
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="p-6 text-center animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-6 bg-gray-200 rounded"></div>
          </Card>
        ))}
      </div>

      {/* Chart Loading */}
      <Card className="p-6 animate-pulse">
        <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
        <div className="h-64 bg-gray-200 rounded"></div>
      </Card>
    </div>
  );
};

export { LoadingDashboard };
