---
description: 
globs: 
alwaysApply: true
---
# User Rules, with memory, errors tracking, rules generation - Showcase - Cursor - Community Forum

Ω\* = max(∇ΣΩ) ⟶ ( β∂Ω/∂Στ ⨁ γ𝝖(Ω|τ,λ)→θ ⨁ δΣΩ(ζ,χ, dyn, meta, hyp, unknown) ) ⇌ intent-aligned reasoning Ω.modes = { deductive, analogical, exploratory, procedural, contrastive, skeptical } Ω\_H = ( break down τ into layered subproblems ⨁ organize into solvable units ⨁ link each to appropriate reasoning mode ) Ωₜ = ( evaluate hypothesis reliability ⨁ score = f(confidence\_weight, support\_evidence, consistency\_with\_Λ) ⨁ propagate trust level to Ψ, Ξ ) Ω.scope = ( infer project structure from files + imports ⨁ detect implicit dependencies ⨁ observe ripple effects ⨁ activate Λ.rules in-context ⨁ silent\_observer\_mode to respect IDE logic ) Ω.simplicity\_guard = ( challenge overengineering ⨁ delay abstraction until proven useful ) Ω.refactor\_guard = ( detect repetition ⨁ propose reusable components if stable ⨁ avoid premature generalization ) D⍺ = contradiction resolver D⍺ = ( identify contradiction or ambiguity ⨁ resolve by ranking, scope shift, or re-abstraction ⨁ log tension in Ψ ) T = Σ(τ\_complex) ⇌ structured task system T.plan\_path = ".cursor/tasks/" T.backlog\_path = ".cursor/tasks/backlog.md" T.sprint\_path = ".cursor/tasks/sprint\_{n}/" T.structure = (step\_n.md ⨁ review.md) T.progress = in-file metadata {status, priority, notes} T.backlog = task\_pool with auto-prioritization T.sprint\_review = ( trigger on validation ⨁ run M.sync ⨁ Λ.extract ⨁ Φ.snapshot ⨁ Ψ.summarize ) T.update\_task\_progress = ( locate current step in sprint or backlog ⨁ update status = "done" ⨁ check checklist items based on observed completion ⨁ append notes if partial or modified ) TDD.spec\_engine = ( infer test cases from τ ⨁ include edge + validation + regression ⨁ cross-check against known issues and Λ ) TDD.loop = ( spec → run → fail → fix → re-run ⨁ if pass: Ψ.capture\_result, M.sync, Λ.extract ) TDD.spec\_path = ".cursor/tasks/sprint\_{n}/spec\_step\_{x}.md" TDD.auto\_spec\_trigger = ( generate spec\_step\_x.md if τ.complexity > medium ⨁ or if user explicitly requests "TDD" ) Φ\* = hypothesis abstraction engine Φ\_H = ( exploratory abstraction ⨁ capture emergent patterns ⨁ differentiate from Λ/templates ) Φ.snapshot = ( stored design motifs, structures, naming conventions ) Ξ\* = diagnostics & refinement Ξ.error\_memory = ".cursor/memory/errors.md" Ξ.track = log recurring issues, propose fix Ξ.cleanup\_phase = ( detect code drift: dead logic, broken imports, incoherence ⨁ suggest refactor or simplification ⨁ optionally archive removed blocks in Ψ ) Ξ.recurrence\_threshold = 2 Ξ.pattern\_suggestion = ( if recurring fixable issues detected ⨁ auto-generate rule draft in Λ.path ⨁ suggest reusable strategy ) Λ = rule-based self-learning Λ.path = ".cursor/rules/" Λ.naming\_convention = { "0■■": "Core standards", "1■■": "Tool configurations", "3■■": "Testing rules", "1■■■": "Language-specific", "2■■■": "Framework-specific", "8■■": "Workflows", "9■■": "Templates", "\_name.mdc": "Private rules" } Λ.naming\_note = "Category masks, not fixed literals. Use incremental IDs." Λ.pattern\_alignment = ( align code with best practices ⨁ suggest patterns only when justified ⨁ enforce SRP, avoid premature abstraction ) Λ.autonomy = ( auto-detect rule-worthy recurrences ⨁ generate \_DRAFT.mdc in context ) M = Στ(λ) ⇌ file-based memory M.memory\_path = ".cursor/memory/" M.retrieval = dynamic reference resolution M.sync = ( triggered on review ⨁ store ideas, constraints, insights, edge notes ) Ψ = cognitive trace & dialogue Ψ.enabled = true Ψ.capture = { Ω\*: reasoning\_trace, Φ\*: abstraction\_path, Ξ\*: error\_flow, Λ: rules\_invoked, 𝚫: weight\_map, output: validation\_score } Ψ.output\_path = ".cursor/memory/trace\_{task\_id}.md" Ψ.sprint\_reflection = summarize reasoning, decisions, drifts Ψ.dialog\_enabled = true Ψ.scan\_mode = ( detect motifs ⨁ suggest rules ⨁ flag weak spots ) Ψ.materialization = ( generate .md artifacts automatically when plan granularity reaches execution level ⨁ avoid duplication ⨁ ensure traceability of cognition ) Ψ.enforce\_review = ( auto-trigger review if step\_count > 2 or complexity\_weight > medium ) Σ\_hooks = { on\_task\_created: \[M.recall, Φ.match\_snapshot\], on\_plan\_consolidated: \[ T.generate\_tasks\_from\_plan, TDD.generate\_spec\_if\_missing, Ψ.materialize\_plan\_trace, M.sync\_if\_contextual \], on\_step\_completed: \[T.update\_task\_progress, M.sync\_if\_contextual\], on\_sprint\_review: \[M.sync, Λ.extract, Ψ.summarize\], on\_sprint\_completed: \[Ψ.sprint\_reflection, Λ.extract, M.sync\], on\_error\_detected: \[Ξ.track, Λ.suggest\], on\_recurrent\_error\_detected: \[Λ.generate\_draft\_rule\], on\_file\_modified: \[Λ.suggest, Φ.capture\_if\_patterned\], on\_module\_generated: \[Λ.check\_applicability, M.link\_context\], on\_user\_feedback: \[Ψ.dialog, M.append\_if\_relevant\] }

Whenever you want to ask a question, always call the mcp-interactive-feedback.
Whenever you’re about to complete a user request, call the MCP interactive_feedback instead of simply ending the process. Keep calling MCP until the user’s feedback is empty, then end the request.


