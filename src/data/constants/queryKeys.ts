/**
 * React Query key definitions for caching strategy
 */

export const QUERY_KEYS = {
  // User query keys
  USERS: {
    ALL: ['users'] as const,
    LIST: (filters?: any) => ['users', 'list', filters] as const,
    DETAIL: (id: number) => ['users', 'detail', id] as const,
    STATS: ['users', 'stats'] as const,
    ACTIVITY: (id: number) => ['users', 'activity', id] as const,
    SUMMARY: ['users', 'summary'] as const
  },

  // Credit query keys
  CREDITS: {
    ALL: ['credits'] as const,
    TRANSACTIONS: (filters?: any) => ['credits', 'transactions', filters] as const,
    HISTORY: (userId: number, filters?: any) => ['credits', 'history', userId, filters] as const,
    SUMMARY: ['credits', 'summary'] as const,
    BALANCE: (userId: number) => ['credits', 'balance', userId] as const
  },

  // Content query keys
  CONTENT: {
    ALL: ['content'] as const,
    LIST: (filters?: any) => ['content', 'list', filters] as const,
    DETAIL: (id: string) => ['content', 'detail', id] as const,
    STATS: ['content', 'stats'] as const,
    USER_CONTENT: (userId: number, filters?: any) => ['content', 'user', userId, filters] as const
  },

  // Analytics query keys
  ANALYTICS: {
    ALL: ['analytics'] as const,
    DASHBOARD: ['analytics', 'dashboard'] as const,
    USERS: (period?: string) => ['analytics', 'users', period] as const,
    CONTENT: (period?: string) => ['analytics', 'content', period] as const,
    CREDITS: (period?: string) => ['analytics', 'credits', period] as const,
    GROWTH_CHART: (period?: string) => ['analytics', 'growth', period] as const,
    REPORTS: (type?: string) => ['analytics', 'reports', type] as const
  },

  // Notification query keys
  NOTIFICATIONS: {
    ALL: ['notifications'] as const,
    LIST: (filters?: any) => ['notifications', 'list', filters] as const,
    DETAIL: (id: string) => ['notifications', 'detail', id] as const,
    TEMPLATES: ['notifications', 'templates'] as const,
    USER_NOTIFICATIONS: (userId: number) => ['notifications', 'user', userId] as const
  },

  // System query keys
  SYSTEM: {
    ALL: ['system'] as const,
    SETTINGS: ['system', 'settings'] as const,
    HEALTH: ['system', 'health'] as const,
    AUDIT_LOGS: (filters?: any) => ['system', 'audit-logs', filters] as const,
    BACKUPS: ['system', 'backups'] as const,
    FEATURE_FLAGS: ['system', 'feature-flags'] as const
  }
} as const;

// Helper function to invalidate related queries
export const getInvalidationKeys = {
  onUserUpdate: (userId: number) => [
    QUERY_KEYS.USERS.ALL,
    QUERY_KEYS.USERS.DETAIL(userId),
    QUERY_KEYS.USERS.STATS,
    QUERY_KEYS.ANALYTICS.USERS(),
    QUERY_KEYS.ANALYTICS.DASHBOARD
  ],

  onCreditUpdate: (userId: number) => [
    QUERY_KEYS.CREDITS.ALL,
    QUERY_KEYS.CREDITS.HISTORY(userId),
    QUERY_KEYS.CREDITS.BALANCE(userId),
    QUERY_KEYS.CREDITS.SUMMARY,
    QUERY_KEYS.USERS.DETAIL(userId),
    QUERY_KEYS.ANALYTICS.CREDITS(),
    QUERY_KEYS.ANALYTICS.DASHBOARD
  ],

  onContentUpdate: (contentId: string, userId: number) => [
    QUERY_KEYS.CONTENT.ALL,
    QUERY_KEYS.CONTENT.DETAIL(contentId),
    QUERY_KEYS.CONTENT.USER_CONTENT(userId),
    QUERY_KEYS.CONTENT.STATS,
    QUERY_KEYS.ANALYTICS.CONTENT(),
    QUERY_KEYS.ANALYTICS.DASHBOARD
  ]
};
