/**
 * DashboardStats - Enhanced stats cards component
 */

import React from 'react';
import { StatsData } from '@/models/types';
import { EnhancedStatsCard } from '@/components/EnhancedStatsCard';

interface DashboardStatsProps {
  statsData: StatsData[];
  isLoading: boolean;
}

export const DashboardStats: React.FC<DashboardStatsProps> = ({
  statsData,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="grid-responsive-1-2-3 gap-4 sm:gap-6">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid-responsive-1-2-3 gap-4 sm:gap-6">
      {statsData.map((stat, index) => (
        <EnhancedStatsCard key={index} {...stat} />
      ))}
    </div>
  );
};
