/**
 * Permission checking và role-based access utilities
 */

import { USER_ROLES, PERMISSION_LEVELS } from '@/models/constants';

// Permission types
export type Permission = keyof typeof PERMISSION_LEVELS;
export type Role = keyof typeof USER_ROLES;

// User permission interface
export interface UserPermissions {
  role: Role;
  permissions: Permission[];
}

// Permission definitions for each role
const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  [USER_ROLES.ADMIN]: ['read', 'write', 'delete', 'admin'],
  [USER_ROLES.MODERATOR]: ['read', 'write'],
  [USER_ROLES.USER]: ['read']
};

// Resource-based permissions
export interface ResourcePermissions {
  users: Permission[];
  credits: Permission[];
  content: Permission[];
  analytics: Permission[];
  notifications: Permission[];
  settings: Permission[];
}

const DEFAULT_RESOURCE_PERMISSIONS: Record<Role, ResourcePermissions> = {
  [USER_ROLES.ADMIN]: {
    users: ['read', 'write', 'delete', 'admin'],
    credits: ['read', 'write', 'delete', 'admin'],
    content: ['read', 'write', 'delete', 'admin'],
    analytics: ['read', 'write', 'admin'],
    notifications: ['read', 'write', 'delete', 'admin'],
    settings: ['read', 'write', 'admin']
  },
  [USER_ROLES.MODERATOR]: {
    users: ['read', 'write'],
    credits: ['read', 'write'],
    content: ['read', 'write', 'delete'],
    analytics: ['read'],
    notifications: ['read', 'write'],
    settings: ['read']
  },
  [USER_ROLES.USER]: {
    users: ['read'],
    credits: ['read'],
    content: ['read'],
    analytics: [],
    notifications: ['read'],
    settings: []
  }
};

// Permission checking functions
export const hasPermission = (
  userRole: Role,
  requiredPermission: Permission
): boolean => {
  const rolePermissions = ROLE_PERMISSIONS[userRole];
  return rolePermissions.includes(requiredPermission);
};

export const hasResourcePermission = (
  userRole: Role,
  resource: keyof ResourcePermissions,
  requiredPermission: Permission
): boolean => {
  const resourcePermissions = DEFAULT_RESOURCE_PERMISSIONS[userRole][resource];
  return resourcePermissions.includes(requiredPermission);
};

export const hasAnyPermission = (
  userRole: Role,
  requiredPermissions: Permission[]
): boolean => {
  return requiredPermissions.some(permission => hasPermission(userRole, permission));
};

export const hasAllPermissions = (
  userRole: Role,
  requiredPermissions: Permission[]
): boolean => {
  return requiredPermissions.every(permission => hasPermission(userRole, permission));
};

// Role checking functions
export const isAdmin = (userRole: Role): boolean => {
  return userRole === USER_ROLES.ADMIN;
};

export const isModerator = (userRole: Role): boolean => {
  return userRole === USER_ROLES.MODERATOR;
};

export const isUser = (userRole: Role): boolean => {
  return userRole === USER_ROLES.USER;
};

export const isAtLeastModerator = (userRole: Role): boolean => {
  return isAdmin(userRole) || isModerator(userRole);
};

// Permission-based component rendering
export const canViewUsers = (userRole: Role): boolean => {
  return hasResourcePermission(userRole, 'users', 'read');
};

export const canEditUsers = (userRole: Role): boolean => {
  return hasResourcePermission(userRole, 'users', 'write');
};

export const canDeleteUsers = (userRole: Role): boolean => {
  return hasResourcePermission(userRole, 'users', 'delete');
};

export const canManageCredits = (userRole: Role): boolean => {
  return hasResourcePermission(userRole, 'credits', 'write');
};

export const canViewAnalytics = (userRole: Role): boolean => {
  return hasResourcePermission(userRole, 'analytics', 'read');
};

export const canManageSettings = (userRole: Role): boolean => {
  return hasResourcePermission(userRole, 'settings', 'write');
};

export const canSendNotifications = (userRole: Role): boolean => {
  return hasResourcePermission(userRole, 'notifications', 'write');
};

// Permission-based navigation
export const getAvailableNavItems = (userRole: Role) => {
  const navItems = [];
  
  // Dashboard is always available
  navItems.push('dashboard');
  
  if (canViewUsers(userRole)) {
    navItems.push('users');
  }
  
  if (hasResourcePermission(userRole, 'content', 'read')) {
    navItems.push('content');
  }
  
  if (canViewAnalytics(userRole)) {
    navItems.push('analytics');
  }
  
  if (hasResourcePermission(userRole, 'notifications', 'read')) {
    navItems.push('notifications');
  }
  
  if (hasResourcePermission(userRole, 'settings', 'read')) {
    navItems.push('settings');
  }
  
  return navItems;
};

// Permission error messages
export const getPermissionErrorMessage = (action: string): string => {
  return `Bạn không có quyền ${action}. Vui lòng liên hệ quản trị viên.`;
};

// Hook for permission checking
export const usePermissions = (userRole: Role) => {
  return {
    // General permissions
    hasPermission: (permission: Permission) => hasPermission(userRole, permission),
    hasResourcePermission: (resource: keyof ResourcePermissions, permission: Permission) => 
      hasResourcePermission(userRole, resource, permission),
    
    // Role checks
    isAdmin: isAdmin(userRole),
    isModerator: isModerator(userRole),
    isUser: isUser(userRole),
    isAtLeastModerator: isAtLeastModerator(userRole),
    
    // Specific permissions
    canViewUsers: canViewUsers(userRole),
    canEditUsers: canEditUsers(userRole),
    canDeleteUsers: canDeleteUsers(userRole),
    canManageCredits: canManageCredits(userRole),
    canViewAnalytics: canViewAnalytics(userRole),
    canManageSettings: canManageSettings(userRole),
    canSendNotifications: canSendNotifications(userRole),
    
    // Navigation
    availableNavItems: getAvailableNavItems(userRole)
  };
};
