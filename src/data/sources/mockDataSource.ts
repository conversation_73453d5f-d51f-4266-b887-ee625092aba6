/**
 * Mock data source matching EXACTLY current component data structures
 * Extracted from Dashboard.tsx and UserCreditManagement.tsx
 */

import { 
  User, 
  StatsData, 
  QuickStats, 
  AnalyticsMetrics,
  CreditTransaction,
  ContentItem,
  Notification
} from '@/models/types';
import { 
  Users, 
  CreditCard, 
  Video,
  Image,
  Bell,
  AlertTriangle
} from 'lucide-react';

// Mock users data - EXACTLY matching UserCreditManagement.tsx
export const mockUsers: User[] = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    email: 'nguy<PERSON><PERSON>@email.com',
    phone: '+84 123 456 789',
    credits: 150,
    videosCreated: 25,
    photosCreated: 80,
    totalUsed: 450,
    totalAdded: 600,
    monthlyStats: {
      videosCompleted: 12,
      videosErrors: 2,
      photosCompleted: 45,
      photosErrors: 3,
      creditsUsed: 180,
      creditsAdded: 200
    },
    lastActivity: '2024-01-15 14:30',
    joinDate: '2023-06-15',
    status: 'active'
  },
  {
    id: 2,
    name: 'T<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+84 987 654 321',
    credits: 50,
    videosCreated: 8,
    photosCreated: 30,
    totalUsed: 200,
    totalAdded: 250,
    monthlyStats: {
      videosCompleted: 5,
      videosErrors: 1,
      photosCompleted: 20,
      photosErrors: 2,
      creditsUsed: 75,
      creditsAdded: 100
    },
    lastActivity: '2024-01-14 09:15',
    joinDate: '2023-08-20',
    status: 'active'
  },
  {
    id: 3,
    name: 'Lê Văn C',
    email: '<EMAIL>',
    phone: '+84 555 666 777',
    credits: 200,
    videosCreated: 45,
    photosCreated: 120,
    totalUsed: 800,
    totalAdded: 1000,
    monthlyStats: {
      videosCompleted: 18,
      videosErrors: 3,
      photosCompleted: 65,
      photosErrors: 1,
      creditsUsed: 280,
      creditsAdded: 300
    },
    lastActivity: '2024-01-15 16:45',
    joinDate: '2023-03-10',
    status: 'premium'
  },
  {
    id: 4,
    name: 'Phạm Thị D',
    email: '<EMAIL>',
    phone: '+84 111 222 333',
    credits: 75,
    videosCreated: 15,
    photosCreated: 50,
    totalUsed: 300,
    totalAdded: 375,
    monthlyStats: {
      videosCompleted: 8,
      videosErrors: 1,
      photosCompleted: 25,
      photosErrors: 0,
      creditsUsed: 120,
      creditsAdded: 150
    },
    lastActivity: '2024-01-13 11:20',
    joinDate: '2023-09-05',
    status: 'active'
  },
  {
    id: 5,
    name: 'Hoàng Văn E',
    email: '<EMAIL>',
    phone: '+84 444 555 666',
    credits: 0,
    videosCreated: 2,
    photosCreated: 5,
    totalUsed: 100,
    totalAdded: 100,
    monthlyStats: {
      videosCompleted: 1,
      videosErrors: 1,
      photosCompleted: 3,
      photosErrors: 2,
      creditsUsed: 50,
      creditsAdded: 0
    },
    lastActivity: '2024-01-10 08:45',
    joinDate: '2023-12-01',
    status: 'inactive'
  }
];

// Mock stats data - EXACTLY matching Dashboard.tsx statsData
export const mockStatsData: StatsData[] = [
  {
    title: 'Tổng người dùng cá nhân',
    value: '12,450',
    change: '+12.5%',
    changeType: 'positive',
    icon: Users,
    gradient: 'bg-gradient-to-r from-blue-500 to-blue-600',
    subtitle: '89 đang hoạt động',
    trend: [20, 35, 25, 40, 30, 45, 50]
  },
  {
    title: 'Credits cá nhân đã dùng',
    value: '28,400',
    change: '+23.5%',
    changeType: 'positive',
    icon: CreditCard,
    gradient: 'bg-gradient-to-r from-green-500 to-green-600',
    subtitle: '456 hôm nay',
    trend: [30, 20, 45, 35, 50, 40, 60]
  },
  {
    title: 'Videos cá nhân',
    value: '8,942',
    change: '+18.2%',
    changeType: 'positive',
    icon: Video,
    gradient: 'bg-gradient-to-r from-purple-500 to-purple-600',
    subtitle: '342 hôm nay',
    trend: [15, 25, 20, 35, 25, 40, 45]
  }
];

// Mock quick stats - EXACTLY matching Dashboard.tsx quickStats
export const mockQuickStats: QuickStats[] = [
  { label: 'Credits còn lại hệ thống', value: '2.4M', color: 'text-green-600' },
  { label: 'Người dùng mới hôm nay', value: '12', color: 'text-blue-600' },
  { label: 'Credits đã phát hành', value: '3.2M', color: 'text-purple-600' }
];

// Helper functions to calculate aggregated data
export const getAggregatedUserStats = () => {
  const totalCredits = mockUsers.reduce((sum, user) => sum + user.credits, 0);
  const totalCreditsAdded = mockUsers.reduce((sum, user) => sum + user.totalAdded, 0);
  const totalCreditsUsed = mockUsers.reduce((sum, user) => sum + user.totalUsed, 0);
  const totalVideos = mockUsers.reduce((sum, user) => sum + user.videosCreated, 0);
  const totalPhotos = mockUsers.reduce((sum, user) => sum + user.photosCreated, 0);

  return {
    totalCredits,
    totalCreditsAdded,
    totalCreditsUsed,
    totalVideos,
    totalPhotos,
    totalUsers: mockUsers.length,
    activeUsers: mockUsers.filter(u => u.status === 'active').length,
    premiumUsers: mockUsers.filter(u => u.status === 'premium').length
  };
};

// Mock analytics metrics
export const mockAnalyticsMetrics: AnalyticsMetrics = {
  totalUsers: 12450,
  activeUsers: 11200,
  newUsersToday: 12,
  totalCredits: 2400000,
  creditsUsedToday: 456,
  creditsRemaining: 2400000,
  creditsIssued: 3200000,
  totalVideos: 8942,
  videosToday: 342,
  totalPhotos: 15680,
  photosToday: 128
};
