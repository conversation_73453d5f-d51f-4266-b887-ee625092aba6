/**
 * useCredits Hook - Credit operations với optimistic updates + creditRepository + validation
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { creditRepository } from '@/data/repositories';
import { QUERY_KEYS, QUERY_STALE_TIME, QUERY_CACHE_TIME, getInvalidationKeys } from '@/data/constants';
import { 
  validateCreditTransaction,
  validatePagination,
  type CreditTransactionFormData,
  type PaginationFormData
} from '@/interface/utils/validation';
import { 
  CreditTransaction, 
  CreditBalance, 
  CreditSummary,
  PaginatedResponse 
} from '@/models/types';
import { toast } from '@/hooks/use-toast';

/**
 * Hook to get credit transaction history for user
 */
export const useCreditHistory = (
  userId: number,
  params: PaginationFormData
) => {
  return useQuery({
    queryKey: QUERY_KEYS.CREDITS.HISTORY(userId, params),
    queryFn: async (): Promise<PaginatedResponse<CreditTransaction>> => {
      const validatedParams = validatePagination(params);
      
      const response = await creditRepository.getCreditHistory(userId, validatedParams);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch credit history');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.MEDIUM,
    gcTime: QUERY_CACHE_TIME.MEDIUM,
    enabled: !!userId
  });
};

/**
 * Hook to get credit balance for user
 */
export const useCreditBalance = (userId: number) => {
  return useQuery({
    queryKey: QUERY_KEYS.CREDITS.BALANCE(userId),
    queryFn: async (): Promise<CreditBalance> => {
      const response = await creditRepository.getCreditBalance(userId);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch credit balance');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.SHORT,
    gcTime: QUERY_CACHE_TIME.MEDIUM,
    enabled: !!userId
  });
};

/**
 * Hook to get credit summary for dashboard
 */
export const useCreditSummary = () => {
  return useQuery({
    queryKey: QUERY_KEYS.CREDITS.SUMMARY,
    queryFn: async (): Promise<CreditSummary> => {
      const response = await creditRepository.getCreditSummary();
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch credit summary');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.SHORT,
    gcTime: QUERY_CACHE_TIME.MEDIUM
  });
};

/**
 * Hook to add credits to user account
 */
export const useAddCredits = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: CreditTransactionFormData): Promise<CreditTransaction> => {
      const validatedData = validateCreditTransaction(data);
      
      const response = await creditRepository.addCredits(
        validatedData.userId,
        validatedData.amount,
        validatedData.description,
        validatedData.adminId,
        validatedData.adminName
      );
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to add credits');
      }
      
      return response.data!;
    },
    onMutate: async (data) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.CREDITS.BALANCE(data.userId) });
      
      // Snapshot previous value
      const previousBalance = queryClient.getQueryData<CreditBalance>(
        QUERY_KEYS.CREDITS.BALANCE(data.userId)
      );
      
      // Optimistically update balance
      if (previousBalance) {
        queryClient.setQueryData<CreditBalance>(
          QUERY_KEYS.CREDITS.BALANCE(data.userId),
          {
            ...previousBalance,
            currentBalance: previousBalance.currentBalance + data.amount,
            totalAdded: previousBalance.totalAdded + data.amount
          }
        );
      }
      
      return { previousBalance };
    },
    onError: (error: Error, data, context) => {
      // Rollback optimistic update
      if (context?.previousBalance) {
        queryClient.setQueryData(
          QUERY_KEYS.CREDITS.BALANCE(data.userId),
          context.previousBalance
        );
      }
      
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
    onSuccess: (transaction) => {
      // Invalidate related queries
      const invalidationKeys = getInvalidationKeys.onCreditUpdate(transaction.userId);
      invalidationKeys.forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
      
      toast({
        title: "Thành công",
        description: `Đã thêm ${transaction.amount} credits`,
      });
    },
    onSettled: (_, __, data) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.CREDITS.BALANCE(data.userId) });
    }
  });
};

/**
 * Hook to subtract credits from user account
 */
export const useSubtractCredits = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: CreditTransactionFormData): Promise<CreditTransaction> => {
      const validatedData = validateCreditTransaction(data);
      
      const response = await creditRepository.subtractCredits(
        validatedData.userId,
        validatedData.amount,
        validatedData.description,
        validatedData.adminId,
        validatedData.adminName
      );
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to subtract credits');
      }
      
      return response.data!;
    },
    onMutate: async (data) => {
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.CREDITS.BALANCE(data.userId) });
      
      const previousBalance = queryClient.getQueryData<CreditBalance>(
        QUERY_KEYS.CREDITS.BALANCE(data.userId)
      );
      
      // Optimistically update balance
      if (previousBalance) {
        queryClient.setQueryData<CreditBalance>(
          QUERY_KEYS.CREDITS.BALANCE(data.userId),
          {
            ...previousBalance,
            currentBalance: previousBalance.currentBalance - data.amount
          }
        );
      }
      
      return { previousBalance };
    },
    onError: (error: Error, data, context) => {
      if (context?.previousBalance) {
        queryClient.setQueryData(
          QUERY_KEYS.CREDITS.BALANCE(data.userId),
          context.previousBalance
        );
      }
      
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
    onSuccess: (transaction) => {
      const invalidationKeys = getInvalidationKeys.onCreditUpdate(transaction.userId);
      invalidationKeys.forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
      
      toast({
        title: "Thành công",
        description: `Đã trừ ${transaction.amount} credits`,
      });
    },
    onSettled: (_, __, data) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.CREDITS.BALANCE(data.userId) });
    }
  });
};

/**
 * Hook to use credits for content creation
 */
export const useCreditsForContent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      userId, 
      amount, 
      contentType, 
      contentId 
    }: { 
      userId: number; 
      amount: number; 
      contentType: 'video' | 'photo'; 
      contentId: string; 
    }) => {
      const response = await creditRepository.useCredits(userId, amount, contentType, contentId);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to use credits');
      }
      
      return response.data!;
    },
    onSuccess: (_, { userId }) => {
      const invalidationKeys = getInvalidationKeys.onCreditUpdate(userId);
      invalidationKeys.forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    }
  });
};

/**
 * Helper functions from creditRepository
 */
export const useCreditHelpers = () => {
  return {
    getTransactionStatusInfo: creditRepository.getTransactionStatusInfo.bind(creditRepository),
    getTransactionTypeLabel: creditRepository.getTransactionTypeLabel.bind(creditRepository)
  };
};
