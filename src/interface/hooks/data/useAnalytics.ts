/**
 * useAnalytics Hook - Analytics data với real-time updates + analyticsRepository
 * Replaces hardcoded statsData from Dashboard.tsx
 */

import { useQuery } from '@tanstack/react-query';
import { analyticsRepository } from '@/data/repositories';
import { QUERY_KEYS, QUERY_STALE_TIME, QUERY_CACHE_TIME } from '@/data/constants';
import { 
  DashboardStats, 
  AnalyticsMetrics,
  GrowthChart,
  PerformanceMetrics,
  ReportData 
} from '@/models/types';

/**
 * Hook to get dashboard statistics - replaces Dashboard.tsx statsData and quickStats
 */
export const useDashboardStats = () => {
  return useQuery({
    queryKey: QUERY_KEYS.ANALYTICS.DASHBOARD,
    queryFn: async (): Promise<DashboardStats> => {
      const response = await analyticsRepository.getDashboardStats();
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch dashboard stats');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.SHORT, // Refresh every 1 minute for real-time feel
    gcTime: QUERY_CACHE_TIME.MEDIUM,
    refetchInterval: 30000, // Auto-refresh every 30 seconds
    refetchIntervalInBackground: false
  });
};

/**
 * Hook to get analytics metrics
 */
export const useAnalyticsMetrics = (period: string = 'month') => {
  return useQuery({
    queryKey: QUERY_KEYS.ANALYTICS.USERS(period),
    queryFn: async (): Promise<AnalyticsMetrics> => {
      const response = await analyticsRepository.getAnalyticsMetrics(period);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch analytics metrics');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.MEDIUM,
    gcTime: QUERY_CACHE_TIME.LONG
  });
};

/**
 * Hook to get user growth chart data
 */
export const useUserGrowthChart = (period: string = 'month') => {
  return useQuery({
    queryKey: QUERY_KEYS.ANALYTICS.GROWTH_CHART(period),
    queryFn: async (): Promise<GrowthChart> => {
      const response = await analyticsRepository.getUserGrowthChart(period);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch growth chart data');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.LONG,
    gcTime: QUERY_CACHE_TIME.LONG
  });
};

/**
 * Hook to get performance metrics
 */
export const usePerformanceMetrics = () => {
  return useQuery({
    queryKey: ['analytics', 'performance'],
    queryFn: async (): Promise<PerformanceMetrics> => {
      const response = await analyticsRepository.getPerformanceMetrics();
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch performance metrics');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.MEDIUM,
    gcTime: QUERY_CACHE_TIME.MEDIUM
  });
};

/**
 * Hook to generate analytics report
 */
export const useGenerateReport = (type: 'daily' | 'weekly' | 'monthly' | 'yearly') => {
  return useQuery({
    queryKey: QUERY_KEYS.ANALYTICS.REPORTS(type),
    queryFn: async (): Promise<ReportData> => {
      const response = await analyticsRepository.generateReport(type);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to generate report');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.VERY_LONG,
    gcTime: QUERY_CACHE_TIME.VERY_LONG,
    enabled: false // Only run when explicitly triggered
  });
};

/**
 * Hook for real-time dashboard updates
 */
export const useRealTimeDashboard = () => {
  const dashboardStats = useDashboardStats();
  const analyticsMetrics = useAnalyticsMetrics();
  const performanceMetrics = usePerformanceMetrics();
  
  return {
    // Dashboard stats (statsData and quickStats from Dashboard.tsx)
    statsData: dashboardStats.data ? [
      dashboardStats.data.totalUsers,
      dashboardStats.data.creditsUsed,
      dashboardStats.data.videosCreated
    ] : [],
    quickStats: dashboardStats.data?.quickStats || [],
    
    // Loading states
    isLoadingStats: dashboardStats.isLoading,
    isLoadingMetrics: analyticsMetrics.isLoading,
    isLoadingPerformance: performanceMetrics.isLoading,
    
    // Error states
    statsError: dashboardStats.error,
    metricsError: analyticsMetrics.error,
    performanceError: performanceMetrics.error,
    
    // Data
    metrics: analyticsMetrics.data,
    performance: performanceMetrics.data,
    
    // Refetch functions
    refetchStats: dashboardStats.refetch,
    refetchMetrics: analyticsMetrics.refetch,
    refetchPerformance: performanceMetrics.refetch,
    
    // Combined loading state
    isLoading: dashboardStats.isLoading || analyticsMetrics.isLoading || performanceMetrics.isLoading,
    
    // Combined error state
    hasError: !!dashboardStats.error || !!analyticsMetrics.error || !!performanceMetrics.error
  };
};

/**
 * Hook for analytics page data
 */
export const useAnalyticsPage = (period: string = 'month') => {
  const metrics = useAnalyticsMetrics(period);
  const growthChart = useUserGrowthChart(period);
  const performance = usePerformanceMetrics();
  
  return {
    metrics: metrics.data,
    growthChart: growthChart.data,
    performance: performance.data,
    
    isLoading: metrics.isLoading || growthChart.isLoading || performance.isLoading,
    error: metrics.error || growthChart.error || performance.error,
    
    refetch: () => {
      metrics.refetch();
      growthChart.refetch();
      performance.refetch();
    }
  };
};
