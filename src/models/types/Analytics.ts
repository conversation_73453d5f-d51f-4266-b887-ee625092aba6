/**
 * Analytics types for Dashboard statistics
 * Extracted from Dashboard.tsx statsData structure
 */

import { LucideIcon } from 'lucide-react';

export interface StatsData {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: LucideIcon;
  gradient: string;
  subtitle: string;
  trend: number[];
}

export interface QuickStats {
  label: string;
  value: string;
  color: string;
}

export interface DashboardStats {
  totalUsers: StatsData;
  creditsUsed: StatsData;
  videosCreated: StatsData;
  quickStats: QuickStats[];
}

export interface AnalyticsMetrics {
  totalUsers: number;
  activeUsers: number;
  newUsersToday: number;
  totalCredits: number;
  creditsUsedToday: number;
  creditsRemaining: number;
  creditsIssued: number;
  totalVideos: number;
  videosToday: number;
  totalPhotos: number;
  photosToday: number;
}

export interface TrendData {
  date: string;
  users: number;
  credits: number;
  videos: number;
  photos: number;
}

export interface GrowthChart {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    tension: number;
  }[];
}

export interface PerformanceMetrics {
  successRate: {
    video: number;
    photo: number;
  };
  averageProcessingTime: {
    video: number;
    photo: number;
  };
  errorRate: {
    video: number;
    photo: number;
  };
}

export interface ReportData {
  id: string;
  title: string;
  type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  generatedAt: string;
  data: AnalyticsMetrics;
  charts: GrowthChart[];
}
