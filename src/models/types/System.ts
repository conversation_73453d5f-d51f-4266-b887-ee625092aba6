/**
 * System settings and configuration types
 */

export interface SystemSettings {
  id: string;
  category: SettingCategory;
  key: string;
  value: any;
  type: SettingType;
  description: string;
  isEditable: boolean;
  isPublic: boolean;
  updatedAt: string;
  updatedBy: {
    id: number;
    name: string;
  };
}

export type SettingCategory = 
  | 'general' 
  | 'credits' 
  | 'content' 
  | 'notifications' 
  | 'security' 
  | 'performance' 
  | 'integrations';

export type SettingType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'json' 
  | 'array' 
  | 'file';

export interface FeatureFlag {
  id: string;
  name: string;
  key: string;
  description: string;
  isEnabled: boolean;
  rolloutPercentage: number;
  targetUsers?: number[];
  conditions?: FeatureCondition[];
  createdAt: string;
  updatedAt: string;
}

export interface FeatureCondition {
  type: 'user_status' | 'user_credits' | 'user_join_date' | 'custom';
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  lastChecked: string;
  services: ServiceHealth[];
  metrics: SystemMetrics;
}

export interface ServiceHealth {
  name: string;
  status: 'up' | 'down' | 'degraded';
  responseTime: number;
  lastChecked: string;
  errorRate: number;
}

export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    inbound: number;
    outbound: number;
  };
  database: {
    connections: number;
    queryTime: number;
    size: number;
  };
}

export interface AuditLog {
  id: string;
  action: string;
  resource: string;
  resourceId: string;
  userId: number;
  userName: string;
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
}

export interface BackupInfo {
  id: string;
  type: 'full' | 'incremental';
  status: 'running' | 'completed' | 'failed';
  size: number;
  createdAt: string;
  completedAt?: string;
  location: string;
  retentionDays: number;
}
