/**
 * useFilters Hook - Table filtering và sorting logic
 */

import { useState, useCallback, useMemo } from 'react';
import { FilterOptions, SortOption } from '@/models/types';

interface UseFiltersProps<T = any> {
  initialFilters?: Partial<T>;
  initialSort?: SortOption;
}

export const useFilters = <T extends Record<string, any>>({
  initialFilters = {},
  initialSort
}: UseFiltersProps<T> = {}) => {
  const [filters, setFilters] = useState<Partial<T>>(initialFilters);
  const [sortBy, setSortBy] = useState<string | undefined>(initialSort?.field);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(initialSort?.direction || 'asc');

  const updateFilter = useCallback((key: keyof T, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const removeFilter = useCallback((key: keyof T) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  const updateSort = useCallback((field: string, direction?: 'asc' | 'desc') => {
    if (field === sortBy) {
      // Toggle direction if same field
      setSortOrder(prev => direction || (prev === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortBy(field);
      setSortOrder(direction || 'asc');
    }
  }, [sortBy]);

  const clearSort = useCallback(() => {
    setSortBy(undefined);
    setSortOrder('asc');
  }, []);

  const hasActiveFilters = useMemo(() => {
    return Object.keys(filters).some(key => {
      const value = filters[key as keyof T];
      return value !== undefined && value !== null && value !== '';
    });
  }, [filters]);

  const activeFilterCount = useMemo(() => {
    return Object.keys(filters).filter(key => {
      const value = filters[key as keyof T];
      return value !== undefined && value !== null && value !== '';
    }).length;
  }, [filters]);

  return {
    // Filter state
    filters,
    updateFilter,
    removeFilter,
    clearFilters,
    hasActiveFilters,
    activeFilterCount,

    // Sort state
    sortBy,
    sortOrder,
    updateSort,
    clearSort,

    // Combined state
    hasActiveSort: !!sortBy,
    
    // Reset all
    reset: useCallback(() => {
      clearFilters();
      clearSort();
    }, [clearFilters, clearSort])
  };
};
