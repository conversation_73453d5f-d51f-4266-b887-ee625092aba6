/**
 * UserGrowthChart - User growth chart component
 */

import React from 'react';
import { Card } from '@/components/ui/card';
import { GrowthChart } from '@/models/types';

interface UserGrowthChartProps {
  data?: GrowthChart;
  isLoading: boolean;
  error?: string;
}

const UserGrowthChart: React.FC<UserGrowthChartProps> = ({
  data,
  isLoading,
  error
}) => {
  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-600">
          <p>Lỗi tải biểu đồ: {error}</p>
        </div>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500">
          <p>Không có dữ liệu biểu đồ</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold text-slate-900 mb-4">
        Biểu đồ tăng trưởng người dùng
      </h3>
      <div className="h-64 flex items-center justify-center bg-slate-50 rounded">
        <p className="text-slate-500">
          Biểu đồ sẽ được hiển thị ở đây (cần tích hợp chart library)
        </p>
      </div>
    </Card>
  );
};

export { UserGrowthChart };
