/**
 * UI-specific constants (colors, sizes, animations)
 */

// Color constants
export const COLORS = {
  // Primary colors
  PRIMARY: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    900: '#1e3a8a'
  },
  
  // Status colors
  SUCCESS: {
    50: '#f0fdf4',
    100: '#dcfce7',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d'
  },
  
  ERROR: {
    50: '#fef2f2',
    100: '#fee2e2',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c'
  },
  
  WARNING: {
    50: '#fffbeb',
    100: '#fef3c7',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309'
  },
  
  INFO: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    500: '#06b6d4',
    600: '#0891b2',
    700: '#0e7490'
  },
  
  // Neutral colors
  GRAY: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a'
  }
} as const;

// Size constants
export const SIZES = {
  // Spacing
  SPACING: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem'
  },
  
  // Border radius
  RADIUS: {
    none: '0',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    full: '9999px'
  },
  
  // Font sizes
  FONT: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem'
  },
  
  // Component sizes
  BUTTON: {
    sm: { height: '2rem', padding: '0.5rem 0.75rem', fontSize: '0.875rem' },
    md: { height: '2.5rem', padding: '0.625rem 1rem', fontSize: '1rem' },
    lg: { height: '3rem', padding: '0.75rem 1.25rem', fontSize: '1.125rem' }
  },
  
  INPUT: {
    sm: { height: '2rem', padding: '0.25rem 0.5rem', fontSize: '0.875rem' },
    md: { height: '2.5rem', padding: '0.5rem 0.75rem', fontSize: '1rem' },
    lg: { height: '3rem', padding: '0.75rem 1rem', fontSize: '1.125rem' }
  }
} as const;

// Animation constants
export const ANIMATIONS = {
  // Duration
  DURATION: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms'
  },
  
  // Easing
  EASING: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out'
  },
  
  // Common animations
  FADE_IN: {
    from: { opacity: 0 },
    to: { opacity: 1 }
  },
  
  SLIDE_UP: {
    from: { transform: 'translateY(10px)', opacity: 0 },
    to: { transform: 'translateY(0)', opacity: 1 }
  },
  
  SLIDE_DOWN: {
    from: { transform: 'translateY(-10px)', opacity: 0 },
    to: { transform: 'translateY(0)', opacity: 1 }
  },
  
  SCALE_IN: {
    from: { transform: 'scale(0.95)', opacity: 0 },
    to: { transform: 'scale(1)', opacity: 1 }
  }
} as const;

// Z-index constants
export const Z_INDEX = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080
} as const;

// Shadow constants
export const SHADOWS = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)'
} as const;

// Layout constants
export const LAYOUT = {
  SIDEBAR_WIDTH: '280px',
  SIDEBAR_WIDTH_COLLAPSED: '64px',
  HEADER_HEIGHT: '64px',
  FOOTER_HEIGHT: '48px',
  CONTAINER_MAX_WIDTH: '1280px'
} as const;

// Component variants
export const VARIANTS = {
  BUTTON: {
    primary: {
      backgroundColor: COLORS.PRIMARY[600],
      color: 'white',
      hover: { backgroundColor: COLORS.PRIMARY[700] }
    },
    secondary: {
      backgroundColor: COLORS.GRAY[200],
      color: COLORS.GRAY[900],
      hover: { backgroundColor: COLORS.GRAY[300] }
    },
    success: {
      backgroundColor: COLORS.SUCCESS[600],
      color: 'white',
      hover: { backgroundColor: COLORS.SUCCESS[700] }
    },
    danger: {
      backgroundColor: COLORS.ERROR[600],
      color: 'white',
      hover: { backgroundColor: COLORS.ERROR[700] }
    },
    ghost: {
      backgroundColor: 'transparent',
      color: COLORS.GRAY[700],
      hover: { backgroundColor: COLORS.GRAY[100] }
    }
  },
  
  BADGE: {
    primary: {
      backgroundColor: COLORS.PRIMARY[100],
      color: COLORS.PRIMARY[800]
    },
    success: {
      backgroundColor: COLORS.SUCCESS[100],
      color: COLORS.SUCCESS[800]
    },
    error: {
      backgroundColor: COLORS.ERROR[100],
      color: COLORS.ERROR[800]
    },
    warning: {
      backgroundColor: COLORS.WARNING[100],
      color: COLORS.WARNING[800]
    },
    gray: {
      backgroundColor: COLORS.GRAY[100],
      color: COLORS.GRAY[800]
    }
  }
} as const;

// Icon sizes
export const ICON_SIZES = {
  xs: '12px',
  sm: '16px',
  md: '20px',
  lg: '24px',
  xl: '32px',
  '2xl': '48px'
} as const;
