/**
 * DashboardMVI - Direct replacement for Dashboard.tsx
 * Preserves EXACT same UI/UX but uses 3-layer MVI architecture
 * Data now comes from repository layer instead of hardcoded statsData
 */

import { useState } from 'react';
import { 
  Users, 
  Search,
  Bell,
  User,
  CreditCard,
  Video
} from 'lucide-react';
import Sidebar from '@/components/Sidebar';
import EnhancedStatsCard from '@/components/EnhancedStatsCard';
import UserGrowthChart from '@/components/UserGrowthChart';
import ContentManagement from '@/components/ContentManagement';
import Analytics from '@/components/Analytics';
import NotificationManagement from '@/components/NotificationManagement';
import SystemSettings from '@/components/SystemSettings';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';

// Import the new MVI components
import UserCreditManagementMVI from '../UserManagement/UserCreditManagementMVI';
import AnalyticsMVI from '../Analytics/AnalyticsMVI';
import ContentManagementMVI from '../ContentManagement/ContentManagementMVI';

// Data hooks - Repository integration (replaces hardcoded statsData)
import { useDashboardStats } from '@/interface/hooks/data';

interface DashboardProps {
  onLogout: () => void;
}

const DashboardMVI = ({ onLogout }: DashboardProps) => {
  const [activeSection, setActiveSection] = useState('dashboard');

  // Data from repository instead of hardcoded statsData and quickStats
  const { 
    // statsData, 
    // quickStats, 
    isLoading: isLoadingStats 
  } = useDashboardStats();

  const getSectionTitle = (section: string) => {
    switch (section) {
      case 'dashboard': return 'Bảng điều khiển';
      case 'users': return 'Quản lý người dùng & Credits';
      case 'content': return 'Quản lý nội dung';
      case 'analytics': return 'Báo cáo & Phân tích';
      case 'notifications': return 'Quản lý thông báo';
      case 'settings': return 'Cài đặt hệ thống';
      default: return section;
    }
  };

  const getSectionDescription = (section: string) => {
    switch (section) {
      case 'dashboard': return 'Tổng quan platform SaaS cho nội dung cá nhân';
      case 'users': return 'Quản lý người dùng, credits và thống kê chi tiết';
      case 'content': return 'Quản lý videos và photos được tạo';
      case 'analytics': return 'Thống kê và phân tích hiệu suất';
      case 'notifications': return 'Gửi và quản lý thông báo';
      case 'settings': return 'Cấu hình và thiết lập hệ thống';
      default: return 'Quản lý platform tạo video và photo cá nhân bằng credits';
    }
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'users':
        // Use new MVI component instead of old UserCreditManagement
        return <UserCreditManagementMVI />;
      case 'content':
        return <ContentManagementMVI />;
      case 'analytics':
        return <AnalyticsMVI />;
      case 'notifications':
        return <NotificationManagement />;
      case 'settings':
        return <SystemSettings />;
      default:
        return (
          <div className="spacing-responsive">
            {/* Enhanced Stats Grid - Data now from repository */}
            <div className="grid-responsive-1-2-3 gap-4 sm:gap-6">
              {isLoadingStats ? (
                // Loading skeleton
                [...Array(3)].map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="h-32 bg-gray-200 rounded-lg"></div>
                  </div>
                ))
              ) : (
                // statsData.map((stat, index) => (
                //   <EnhancedStatsCard key={index} {...stat} />
                // ))
              )}
            </div>

            {/* Quick Stats Row - Data now from repository */}
            <div className="grid-responsive-1-2-3 gap-4">
              {isLoadingStats ? (
                // Loading skeleton
                [...Array(3)].map((_, index) => (
                  <Card key={index} className="p-4 sm:p-6 text-center animate-pulse">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-6 bg-gray-200 rounded"></div>
                  </Card>
                ))
              ) : (
                quickStats.map((stat, index) => (
                  <Card key={index} className="p-4 sm:p-6 text-center">
                    <p className="text-xs sm:text-sm text-slate-600 mb-1">{stat.label}</p>
                    <p className={`text-lg sm:text-xl font-bold ${stat.color}`}>{stat.value}</p>
                  </Card>
                ))
              )}
            </div>

            {/* Charts */}
            <UserGrowthChart />
          </div>
        );
    }
  };

  return (
    <div className="flex min-h-screen bg-slate-50">
      <Sidebar 
        activeSection={activeSection} 
        onSectionChange={setActiveSection}
        onLogout={onLogout}
      />
      
      <div className="flex-1 overflow-auto w-full min-w-0">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-slate-200 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="min-w-0 flex-1">
              <h1 className="text-xl sm:text-2xl font-bold text-slate-900 truncate">
                {getSectionTitle(activeSection)}
              </h1>
              <p className="text-sm sm:text-base text-slate-600 mt-1">
                {getSectionDescription(activeSection)}
              </p>
            </div>
            
            <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
              <div className="relative hidden sm:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input 
                  placeholder="Tìm kiếm..." 
                  className="pl-10 w-60 lg:w-80 bg-slate-50 border-slate-200 focus:bg-white"
                />
              </div>
              
              <Button variant="ghost" size="icon" className="relative flex-shrink-0">
                <Bell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </Button>
              
              <Button variant="ghost" size="icon" className="flex-shrink-0">
                <User className="w-5 h-5" />
              </Button>
            </div>
          </div>
          
          {/* Mobile Search */}
          <div className="relative mt-4 sm:hidden">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <Input 
              placeholder="Tìm kiếm..." 
              className="pl-10 w-full bg-slate-50 border-slate-200 focus:bg-white"
            />
          </div>
        </header>

        {/* Main Content */}
        <main className="p-4 sm:p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default DashboardMVI;
