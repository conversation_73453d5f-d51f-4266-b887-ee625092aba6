Tôi muốn tái cấu trúc dự án Mega AI Admin hiện tại để triển khai kiến trúc 3 lớp đơn giản (3-layer architecture) theo mô hình MVI (Model-View-Interface) phù hợp cho ứng dụng frontend React, với phân tách trách nhiệm rõ ràng và tuân thủ nguyên tắc Separation of Concerns.

**Mục tiêu tái cấu trúc:**
- Tách biệt business logic khỏi UI components
- Tạo abstraction layer đơn giản cho data access
- Cải thiện testability, maintainability và scalability
- Đảm bảo type safety xuyên suốt các layers
- <PERSON><PERSON><PERSON> nguyên tính đơn giản phù hợp với quy mô dự án frontend
- Tối ưu hóa performance và user experience
- Preserve tất cả existing functionality và UI/UX hiện tại

**Kiến trúc 3 lớp MVI đơn giản cho Frontend:**

1. **Model Layer** (`/src/models/`): 
   - **Types & Interfaces** (`/src/models/types/`): 
     * `User.ts` - User entity với profile, permissions, activity tracking, monthly stats
     * `Credit.ts` - Credit system với transactions, balance, history, usage tracking
     * `Content.ts` - Video/Photo entities với metadata, status, processing info, creation stats
     * `Analytics.ts` - Analytics data structures, metrics, reports, dashboard stats
     * `Notification.ts` - Notification system với types, status, delivery methods
     * `System.ts` - System settings, configurations, feature flags, admin preferences
     * `Common.ts` - Shared types (ApiResponse, PaginationParams, FilterOptions, SearchParams)
   - **Constants** (`/src/models/constants/`): 
     * `userStatus.ts` - UserStatus ('active', 'premium', 'inactive'), UserRole, PermissionLevel enums
     * `creditTypes.ts` - CreditType, TransactionType ('add', 'subtract', 'used'), PaymentStatus enums
     * `contentTypes.ts` - ContentType ('video', 'photo'), ProcessingStatus ('completed', 'error'), QualityLevel enums
     * `appConfig.ts` - Application configuration constants (pagination limits, refresh intervals, feature flags)

2. **Data Layer** (`/src/data/`) - Đơn giản hóa:
   - **Constants** (`/src/data/constants/`):
     * `apiEndpoints.ts` - API URL definitions và endpoint configurations
     * `apiKeys.ts` - API keys, cache keys, storage keys
     * `queryKeys.ts` - React Query key definitions cho caching
   - **Repositories** (`/src/data/repositories/`):
     * `userRepository.ts` - User data access với CRUD operations, credit management, monthly stats calculation
     * `creditRepository.ts` - Credit data management với transaction history, balance calculations
     * `analyticsRepository.ts` - Analytics data aggregation với dashboard stats, growth charts
     * `contentRepository.ts` - Content management với creation tracking, status monitoring
     * `notificationRepository.ts` - Notification CRUD với delivery status tracking
     * `systemRepository.ts` - System settings và configuration management
     * `baseRepository.ts` - Shared repository logic (error handling, response formatting, mock data integration)
   - **Sources** (`/src/data/sources/`):
     * `mockDataSource.ts` - Comprehensive mock data matching current component data structures
     * `localStorageSource.ts` - Browser storage operations cho user preferences, cache
     * `cacheSource.ts` - Client-side caching strategies cho performance optimization

3. **Interface Layer** (`/src/interface/`):
   - **Assets** (`/src/interface/assets/`):
     * `images/` - SVG icons, PNG images, logos, illustrations
     * `fonts/` - Custom font files (woff2, woff, ttf)
     * `styles/` - CSS modules, SCSS files, theme files
     * `icons/` - Custom icon components, SVG sprites
   - **Components** (`/src/interface/components/`):
     * **UI Components** (`/src/interface/components/ui/`): Giữ nguyên toàn bộ shadcn/ui components
     * **Feature Components** (`/src/interface/components/features/`):
       - `Dashboard/` - DashboardStats, EnhancedStatsCard, UserGrowthChart, QuickStats
       - `UserManagement/` - UserTable, UserDetails, CreditManagement, UserFilters, MonthlyStats
       - `Analytics/` - AnalyticsCharts, ReportsTable, MetricCards, TrendAnalysis
       - `ContentManagement/` - ContentList, ContentFilters, BulkActions, StatusTracking
       - `Notifications/` - NotificationCenter, NotificationSettings, AlertBanner
       - `Settings/` - SystemSettings, UserPreferences, SecuritySettings
     * **Layout Components** (`/src/interface/components/layout/`):
       - `Sidebar.tsx` - Navigation với collapsible functionality, quick stats
       - `Header.tsx` - Search, notifications, user menu với responsive design
       - `MainLayout.tsx` - Responsive layout wrapper với mobile support
       - `LoadingLayout.tsx` - Loading states và skeleton components
   - **Hooks** (`/src/interface/hooks/`):
     * **Data Hooks** (`/src/interface/hooks/data/`):
       - `useUsers.ts` - User data management với React Query + userRepository (bao gồm Zod validators)
       - `useCredits.ts` - Credit operations với optimistic updates + creditRepository (bao gồm credit validators)
       - `useAnalytics.ts` - Analytics data với real-time updates + analyticsRepository
       - `useContent.ts` - Content management với status tracking + contentRepository (bao gồm content validators)
     * **UI Hooks** (`/src/interface/hooks/ui/`):
       - `useModal.ts` - Modal state management cho dialogs
       - `useNotifications.ts` - Toast và notification system integration
       - `useFilters.ts` - Table filtering và sorting logic
       - `usePagination.ts` - Pagination logic cho tables
       - `useSearch.ts` - Search functionality với debouncing
   - **Pages** (`/src/interface/pages/`):
     * `DashboardPage.tsx` - Main dashboard orchestration với stats và charts
     * `UserManagementPage.tsx` - User management page container với tabs
     * `AnalyticsPage.tsx` - Analytics page với multiple views
     * `ContentPage.tsx` - Content management page
     * `SettingsPage.tsx` - System settings page
   - **Utils** (`/src/interface/utils/`):
     * `formatters.ts` - Data formatting utilities (currency, dates, numbers)
     * `permissions.ts` - Permission checking và role-based access
     * `responsive.ts` - Responsive utilities và breakpoint helpers
     * `validation.ts` - Zod schemas và validation utilities
     * `constants.ts` - UI-specific constants (colors, sizes, animations)

**Cấu trúc thư mục chi tiết:**
```
/src/
├── models/
│   ├── types/
│   │   ├── User.ts          # User interface với credits, videos, photos, monthlyStats
│   │   ├── Credit.ts        # Credit transaction types, balance tracking
│   │   ├── Content.ts       # Video/Photo content types với metadata
│   │   ├── Analytics.ts     # Dashboard analytics, growth metrics
│   │   ├── Notification.ts  # Notification system types
│   │   ├── System.ts        # System settings, configurations
│   │   ├── Common.ts        # ApiResponse, Pagination, Filter types
│   │   └── index.ts         # Export all types
│   ├── constants/
│   │   ├── userStatus.ts    # 'active', 'premium', 'inactive' status enums
│   │   ├── creditTypes.ts   # Credit transaction type enums
│   │   ├── contentTypes.ts  # Video/Photo content type enums
│   │   ├── appConfig.ts     # App configuration constants
│   │   └── index.ts         # Export all constants
│   └── index.ts
├── data/
│   ├── constants/
│   │   ├── apiEndpoints.ts      # API URL definitions
│   │   ├── apiKeys.ts           # API keys, cache keys, storage keys
│   │   ├── queryKeys.ts         # React Query key definitions
│   │   └── index.ts             # Export all data constants
│   ├── repositories/
│   │   ├── userRepository.ts        # User CRUD, credit management, stats
│   │   ├── creditRepository.ts      # Credit transactions, history
│   │   ├── analyticsRepository.ts   # Dashboard analytics, charts data
│   │   ├── contentRepository.ts     # Content management, tracking
│   │   ├── notificationRepository.ts # Notification CRUD
│   │   ├── systemRepository.ts      # System settings
│   │   ├── baseRepository.ts        # Shared repository logic
│   │   └── index.ts                 # Export all repositories
│   ├── sources/
│   │   ├── mockDataSource.ts        # Mock data matching current structure
│   │   ├── localStorageSource.ts    # Browser storage operations
│   │   ├── cacheSource.ts           # Client-side caching
│   │   └── index.ts                 # Export all sources
│   └── index.ts
├── interface/
│   ├── assets/
│   │   ├── images/
│   │   │   ├── logos/               # Company logos, brand assets
│   │   │   ├── icons/               # Custom SVG icons
│   │   │   ├── illustrations/       # UI illustrations, empty states
│   │   │   └── avatars/             # Default user avatars
│   │   ├── fonts/
│   │   │   ├── inter/               # Inter font family files
│   │   │   └── custom/              # Custom brand fonts
│   │   ├── styles/
│   │   │   ├── themes/              # Theme CSS files
│   │   │   ├── components/          # Component-specific styles
│   │   │   └── animations/          # Custom animation CSS
│   │   └── index.ts                 # Asset exports và path constants
│   ├── components/
│   │   ├── ui/                      # Giữ nguyên shadcn/ui components
│   │   ├── features/
│   │   │   ├── Dashboard/
│   │   │   │   ├── DashboardStats.tsx
│   │   │   │   ├── EnhancedStatsCard.tsx
│   │   │   │   ├── UserGrowthChart.tsx
│   │   │   │   └── index.ts
│   │   │   ├── UserManagement/
│   │   │   │   ├── UserTable.tsx
│   │   │   │   ├── UserDetails.tsx
│   │   │   │   ├── CreditManagement.tsx
│   │   │   │   ├── MonthlyStats.tsx
│   │   │   │   └── index.ts
│   │   │   ├── Analytics/
│   │   │   ├── ContentManagement/
│   │   │   ├── Notifications/
│   │   │   └── Settings/
│   │   ├── layout/
│   │   │   ├── Sidebar.tsx          # Navigation với collapsible
│   │   │   ├── Header.tsx           # Search, notifications, user menu
│   │   │   ├── MainLayout.tsx       # Responsive layout wrapper
│   │   │   └── LoadingLayout.tsx    # Loading states
│   │   └── index.ts
│   ├── hooks/
│   │   ├── data/
│   │   │   ├── useUsers.ts          # User data management + Zod validators
│   │   │   ├── useCredits.ts        # Credit operations + credit validators
│   │   │   ├── useAnalytics.ts      # Analytics data
│   │   │   ├── useContent.ts        # Content management + content validators
│   │   │   └── index.ts
│   │   ├── ui/
│   │   │   ├── useModal.ts          # Modal state management
│   │   │   ├── useNotifications.ts  # Toast notifications
│   │   │   ├── useFilters.ts        # Table filtering
│   │   │   ├── usePagination.ts     # Pagination logic
│   │   │   ├── useSearch.ts         # Search với debouncing
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── pages/
│   │   ├── DashboardPage.tsx        # Main dashboard container
│   │   ├── UserManagementPage.tsx   # User management với tabs
│   │   ├── AnalyticsPage.tsx        # Analytics page
│   │   ├── ContentPage.tsx          # Content management
│   │   ├── SettingsPage.tsx         # System settings
│   │   └── index.ts
│   ├── utils/
│   │   ├── formatters.ts            # Data formatting utilities
│   │   ├── permissions.ts           # Permission checking
│   │   ├── responsive.ts            # Responsive utilities
│   │   ├── validation.ts            # Zod schemas và validation utilities
│   │   ├── constants.ts             # UI-specific constants
│   │   └── index.ts
│   └── index.ts
├── lib/                             # Giữ nguyên existing lib
│   ├── utils.ts
│   ├── queryClient.ts
│   └── index.ts
├── App.tsx                          # Giữ nguyên structure
├── main.tsx                         # Giữ nguyên
└── index.css                        # Giữ nguyên custom CSS
```

**Quy tắc dependency và data flow:**
- **Interface Layer** → **Data Layer** (chỉ thông qua data hooks)
- **Data Layer** → **Model Layer** (sử dụng types, constants)
- **Validators (Zod schemas)** được đặt trong **Interface Utils** (`/src/interface/utils/validation.ts`)
- **API endpoints, keys** được đặt trong **Data Layer constants**
- **Repository** chứa tất cả logic: mock data access, data transformation, error handling
- **Assets** được quản lý trong **Interface Layer** với proper imports và path constants
- **KHÔNG được phép**: Interface components → Repository trực tiếp (phải qua hooks)
- **Data flow**: User Action → Data Hook (với validators) → Repository (với mock data) → React Query Cache → Component Update

**Triển khai theo phases chi tiết:**

**Phase 1 (Bắt đầu ngay)**: Tạo Model Layer và Data Constants
- Phân tích existing components (Dashboard, UserCreditManagement, Login, Sidebar) để extract types
- Tạo User interface với properties từ existing data structures trong `src/components/UserCreditManagement.tsx` (users array)
- Tạo Credit interface với transaction types và history tracking
- Tạo Analytics interface từ statsData trong `src/components/Dashboard.tsx`
- Define constants cho user status ('active', 'premium', 'inactive'), content types trong Model Layer
- Tạo API endpoints, keys, query keys trong Data Layer constants
- Setup TypeScript path aliases: `@/models/*`, `@/data/*`, `@/interface/*`

**Phase 2**: Implement Repository Layer với Mock Data
- Tạo userRepository với mock data matching current users array structure từ UserCreditManagement
- Implement creditRepository với credit management logic
- Tạo analyticsRepository với dashboard stats calculation từ existing statsData
- Tích hợp mock data sources thay thế hardcoded data trong components
- Preserve tất cả existing data structures và business logic

**Phase 3**: Tạo Data Hooks với Validators
- Implement useUsers hook với React Query integration
- Tạo Zod schemas trong `/src/interface/utils/validation.ts` cho user, credit, content validation
- Tạo useCredits hook cho credit operations với credit validation
- Tạo useContent hook với content validators
- Connect repositories với React Query cache
- Maintain existing component behavior và performance

**Phase 4**: Setup Assets Management
- Di chuyển existing assets từ `/public/` sang `/src/interface/assets/`
- Tạo asset path constants trong `/src/interface/assets/index.ts`
- Setup proper imports cho images, fonts, styles
- Maintain existing asset references trong components

**Phase 5**: Refactor Components thành Feature-based Structure
- Extract Dashboard components thành features/Dashboard/
- Refactor UserCreditManagement thành features/UserManagement/
- Preserve tất cả existing UI/UX và responsive design
- Maintain Vietnamese language support
- Update asset imports để sử dụng new asset structure

**Phase 6**: Implement Pages và Layout Components
- Tạo page containers cho routing
- Extract layout components từ existing Dashboard
- Preserve existing navigation và responsive behavior

**Phase 7**: Testing và Optimization
- Add comprehensive TypeScript types
- Implement error boundaries
- Performance optimization với React Query caching
- Add unit tests cho repositories và hooks

**Yêu cầu kỹ thuật cụ thể:**
- **Validators (Zod schemas)** đặt trong Interface Utils (`/src/interface/utils/validation.ts`), KHÔNG trong Model Layer
- **API endpoints, keys** đặt trong Data Layer constants, KHÔNG trong Model Layer
- **Assets** được quản lý trong Interface Layer với proper path constants và imports
- Model Layer chỉ chứa pure types và business constants (enums, status)
- Repositories chứa tất cả business logic (không tách riêng services)
- Mock data phải match exactly với current component data structures
- Giữ nguyên React Query integration và caching strategies
- Preserve tất cả existing functionality, UI components, styling, và assets
- Progressive migration (không breaking changes, có thể chạy song song)
- TypeScript strict mode với comprehensive type coverage
- Maintain existing responsive design và mobile support
- Preserve Vietnamese language support và existing UX patterns

**Bắt đầu với Phase 1 ngay lập tức**: 
1. Phân tích file `src/components/Dashboard.tsx` để extract statsData structure thành Analytics types
2. Phân tích `src/components/UserCreditManagement.tsx` để extract users array structure thành User types với đầy đủ properties: id, name, email, phone, credits, videosCreated, photosCreated, totalUsed, totalAdded, monthlyStats, lastActivity, joinDate, status
3. Tạo comprehensive type definitions trong `models/types/` với đầy đủ properties từ existing data
4. Define constants cho enums hiện tại (user status: 'active', 'premium', 'inactive', content types) trong Model Layer
5. Tạo API endpoints, cache keys trong Data Layer constants
6. Setup TypeScript path aliases trong tsconfig.json
7. Tạo asset management structure trong Interface Layer

**Lưu ý quan trọng về phân tách trách nhiệm:**
- **Model Layer**: Chỉ chứa pure data types và business constants (enums, status)
- **Data Layer**: Chứa API endpoints, cache keys, repositories, data sources
- **Interface Layer**: Chứa validators trong utils, UI components, pages, assets, utilities
- **Assets**: Được quản lý trong Interface Layer với proper organization và path constants