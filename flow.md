Tôi muốn tái cấu trúc dự án Mega AI Admin hiện tại để triển khai kiến trúc 3 lớp MVI (Model-View-Interface) phù hợp cho ứng dụng React frontend, với phân tách trách nhiệm rõ ràng và tuân thủ nguyên tắc Separation of Concerns.

**Mục tiêu tái cấu trúc cụ thể:**
- Tách biệt hoàn toàn business logic khỏi UI components
- Tạo abstraction layer cho data access với repository pattern
- Cải thiện testability, maintainability và scalability
- <PERSON><PERSON><PERSON> bảo type safety xuyên suốt với TypeScript strict mode
- G<PERSON><PERSON> nguyên tính đơn giản phù hợp với quy mô frontend project
- Tối ưu hóa performance với React Query caching
- Preserve 100% existing functionality, UI/UX, styling và assets
- Hỗ trợ progressive migration không breaking changes

**Kiến trúc 3 lớp MVI cho React Frontend:**

**1. Model Layer** (`/src/models/`) - Pure Data & Business Constants:
- **Types & Interfaces** (`/src/models/types/`):
  * `User.ts` - User entity với profile, permissions, activity tracking, monthly stats
  * `Credit.ts` - Credit system với transactions, balance, history, usage tracking  
  * `Content.ts` - Video/Photo entities với metadata, status, processing info
  * `Analytics.ts` - Analytics data structures, metrics, reports, dashboard stats
  * `Notification.ts` - Notification system với types, status, delivery methods
  * `System.ts` - System settings, configurations, feature flags
  * `Common.ts` - Shared types (ApiResponse, PaginationParams, FilterOptions)
- **Constants** (`/src/models/constants/`):
  * `userStatus.ts` - UserStatus enums ('active', 'premium', 'inactive'), UserRole, PermissionLevel
  * `creditTypes.ts` - CreditType, TransactionType enums ('add', 'subtract', 'used'), PaymentStatus
  * `contentTypes.ts` - ContentType enums ('video', 'photo'), ProcessingStatus, QualityLevel
  * `appConfig.ts` - Application configuration constants (pagination limits, refresh intervals)

**2. Data Layer** (`/src/data/`) - Data Access & Business Logic:
- **Constants** (`/src/data/constants/`):
  * `apiEndpoints.ts` - API URL definitions và endpoint configurations
  * `apiKeys.ts` - API keys, cache keys, storage keys
  * `queryKeys.ts` - React Query key definitions cho caching strategy
- **Repositories** (`/src/data/repositories/`) - Chứa TẤT CẢ business logic:
  * `userRepository.ts` - User CRUD operations, credit management, monthly stats calculation
  * `creditRepository.ts` - Credit transaction management, balance calculations, history tracking
  * `analyticsRepository.ts` - Analytics data aggregation, dashboard stats, growth charts
  * `contentRepository.ts` - Content management, creation tracking, status monitoring
  * `notificationRepository.ts` - Notification CRUD, delivery status tracking
  * `systemRepository.ts` - System settings và configuration management
  * `baseRepository.ts` - Shared repository logic (error handling, response formatting, mock integration)
- **Sources** (`/src/data/sources/`):
  * `mockDataSource.ts` - Mock data matching EXACTLY current component data structures
  * `localStorageSource.ts` - Browser storage operations cho user preferences
  * `cacheSource.ts` - Client-side caching strategies cho performance

**3. Interface Layer** (`/src/interface/`) - UI Components & User Interaction:
- **Assets** (`/src/interface/assets/`) - Asset Management:
  * `images/` - SVG icons, PNG images, logos, illustrations với organized subfolders
  * `fonts/` - Custom font files (woff2, woff, ttf) với font family definitions
  * `styles/` - CSS modules, SCSS files, theme files với component-specific styles
  * `icons/` - Custom icon components, SVG sprites với proper exports
- **Components** (`/src/interface/components/`):
  * **UI Components** (`/src/interface/components/ui/`) - Giữ NGUYÊN toàn bộ shadcn/ui
  * **Feature Components** (`/src/interface/components/features/`) - Business feature components:
    - `Dashboard/` - DashboardStats, EnhancedStatsCard, UserGrowthChart, QuickStats
    - `UserManagement/` - UserTable, UserDetails, CreditManagement, UserFilters, MonthlyStats
    - `Analytics/` - AnalyticsCharts, ReportsTable, MetricCards, TrendAnalysis
    - `ContentManagement/` - ContentList, ContentFilters, BulkActions, StatusTracking
    - `Notifications/` - NotificationCenter, NotificationSettings, AlertBanner
    - `Settings/` - SystemSettings, UserPreferences, SecuritySettings
  * **Layout Components** (`/src/interface/components/layout/`):
    - `Sidebar.tsx` - Navigation với collapsible functionality, quick stats
    - `Header.tsx` - Search, notifications, user menu với responsive design
    - `MainLayout.tsx` - Responsive layout wrapper với mobile support
    - `LoadingLayout.tsx` - Loading states và skeleton components
- **Hooks** (`/src/interface/hooks/`) - Data & UI State Management:
  * **Data Hooks** (`/src/interface/hooks/data/`) - Repository integration với React Query:
    - `useUsers.ts` - User data management với React Query + userRepository + Zod validation
    - `useCredits.ts` - Credit operations với optimistic updates + creditRepository + validation
    - `useAnalytics.ts` - Analytics data với real-time updates + analyticsRepository
    - `useContent.ts` - Content management với status tracking + contentRepository + validation
  * **UI Hooks** (`/src/interface/hooks/ui/`) - UI state management:
    - `useModal.ts` - Modal state management cho dialogs
    - `useNotifications.ts` - Toast và notification system integration
    - `useFilters.ts` - Table filtering và sorting logic
    - `usePagination.ts` - Pagination logic cho tables
    - `useSearch.ts` - Search functionality với debouncing
- **Pages** (`/src/interface/pages/`) - Page containers:
  * `DashboardPage.tsx` - Main dashboard orchestration với stats và charts
  * `UserManagementPage.tsx` - User management page container với tabs
  * `AnalyticsPage.tsx` - Analytics page với multiple views
  * `ContentPage.tsx` - Content management page
  * `SettingsPage.tsx` - System settings page
- **Utils** (`/src/interface/utils/`) - Interface utilities:
  * `formatters.ts` - Data formatting utilities (currency, dates, numbers)
  * `permissions.ts` - Permission checking và role-based access
  * `responsive.ts` - Responsive utilities và breakpoint helpers
  * `validation.ts` - Zod schemas và validation utilities (ĐẶT TẠI ĐÂY, KHÔNG trong Model)
  * `constants.ts` - UI-specific constants (colors, sizes, animations)

**Quy tắc dependency nghiêm ngặt:**
- **Interface Layer** → **Data Layer** (CHỈ thông qua data hooks, KHÔNG trực tiếp repository)
- **Data Layer** → **Model Layer** (sử dụng types, constants)
- **Zod validators** ĐẶT trong **Interface Utils** (`/src/interface/utils/validation.ts`)
- **API endpoints, keys** ĐẶT trong **Data Layer constants**
- **Assets** được quản lý trong **Interface Layer** với proper imports và path constants
- **Repository** chứa TẤT CẢ business logic (KHÔNG tách riêng services)
- **Mock data** phải match EXACTLY với current component data structures
- **CẤM**: Interface components → Repository trực tiếp (phải qua hooks)

**Data flow chuẩn:**
User Action → Data Hook (với Zod validators) → Repository (với mock data) → React Query Cache → Component Update

**Triển khai theo phases cụ thể:**

**Phase 1 (Bắt đầu NGAY)** - Model Layer Foundation:
1. Phân tích `src/components/Dashboard.tsx` để extract statsData structure → Analytics types
2. Phân tích `src/components/UserCreditManagement.tsx` để extract users array → User types với properties: id, name, email, phone, credits, videosCreated, photosCreated, totalUsed, totalAdded, monthlyStats, lastActivity, joinDate, status
3. Tạo comprehensive type definitions trong `models/types/` với ĐẦY ĐỦ properties từ existing data
4. Define constants cho enums (user status: 'active', 'premium', 'inactive', content types)
5. Tạo API endpoints, cache keys trong Data Layer constants
6. Setup TypeScript path aliases trong tsconfig.json: `@/models/*`, `@/data/*`, `@/interface/*`
7. Tạo asset management structure trong Interface Layer

**Phase 2** - Repository Layer với Mock Data:
1. Tạo userRepository với mock data matching EXACTLY current users array từ UserCreditManagement
2. Implement creditRepository với credit management logic
3. Tạo analyticsRepository với dashboard stats calculation từ existing statsData
4. Tích hợp mock data sources thay thế hardcoded data
5. Preserve TẤT CẢ existing data structures và business logic

**Phase 3** - Data Hooks với Validators:
1. Implement useUsers hook với React Query integration
2. Tạo Zod schemas trong `/src/interface/utils/validation.ts` cho user, credit, content validation
3. Tạo useCredits hook cho credit operations với credit validation
4. Tạo useContent hook với content validators
5. Connect repositories với React Query cache
6. Maintain existing component behavior và performance

**Phase 4** - Assets Management:
1. Di chuyển existing assets từ `/public/` sang `/src/interface/assets/`
2. Tạo asset path constants trong `/src/interface/assets/index.ts`
3. Setup proper imports cho images, fonts, styles
4. Maintain existing asset references trong components

**Phase 5** - Component Refactoring:
1. Extract Dashboard components thành features/Dashboard/
2. Refactor UserCreditManagement thành features/UserManagement/
3. Preserve TẤT CẢ existing UI/UX và responsive design
4. Maintain Vietnamese language support
5. Update asset imports để sử dụng new structure

**Phase 6** - Pages và Layout:
1. Tạo page containers cho routing
2. Extract layout components từ existing Dashboard
3. Preserve existing navigation và responsive behavior

**Phase 7** - Optimization:
1. Add comprehensive TypeScript types
2. Implement error boundaries
3. Performance optimization với React Query caching

**Yêu cầu kỹ thuật NGHIÊM NGẶT:**
- **Zod schemas** ĐẶT trong Interface Utils, KHÔNG trong Model Layer
- **API endpoints, keys** ĐẶT trong Data Layer constants, KHÔNG trong Model Layer
- **Assets** được quản lý trong Interface Layer với proper path constants
- Model Layer CHỈ chứa pure types và business constants (enums, status)
- Repositories chứa TẤT CẢ business logic (KHÔNG tách riêng services)
- Mock data phải match EXACTLY với current component data structures
- Giữ NGUYÊN React Query integration và caching strategies
- Preserve TẤT CẢ existing functionality, UI components, styling, assets
- Progressive migration (KHÔNG breaking changes, có thể chạy song song)
- TypeScript strict mode với comprehensive type coverage
- Maintain existing responsive design và mobile support
- Preserve Vietnamese language support và existing UX patterns

**Bắt đầu NGAY với Phase 1:**
1. Phân tích existing components để extract exact data structures
2. Tạo comprehensive type definitions với đầy đủ properties
3. Setup TypeScript path aliases
4. Tạo asset management structure
5. Validate rằng tất cả existing functionality được preserve

**Lưu ý đặc biệt về object-oriented approach:**
- Các đối tượng triển khai theo chuẩn hướng đối tượng
- Tổng hợp thành thông tin trên giao diện
- KHÔNG đưa các tính năng UI thành 1 đối tượng
- Maintain clear separation between business objects và UI features