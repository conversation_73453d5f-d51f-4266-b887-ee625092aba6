/**
 * Content management types for videos and photos
 */

export interface ContentItem {
  id: string;
  userId: number;
  userName: string;
  type: ContentType;
  title: string;
  description?: string;
  status: ProcessingStatus;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  creditsUsed: number;
  metadata: ContentMetadata;
  fileInfo: FileInfo;
  processingInfo?: ProcessingInfo;
}

export type ContentType = 'video' | 'photo';

export type ProcessingStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'cancelled';

export interface ContentMetadata {
  originalPrompt?: string;
  style?: string;
  quality: QualityLevel;
  duration?: number; // for videos in seconds
  dimensions?: {
    width: number;
    height: number;
  };
  tags?: string[];
  isPublic: boolean;
}

export type QualityLevel = 'low' | 'medium' | 'high' | 'ultra';

export interface FileInfo {
  originalName?: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  url?: string;
  thumbnailUrl?: string;
  downloadUrl?: string;
}

export interface ProcessingInfo {
  startedAt: string;
  completedAt?: string;
  processingTime?: number; // in seconds
  errorMessage?: string;
  retryCount: number;
  maxRetries: number;
  progress?: number; // 0-100
}

export interface ContentFilters {
  type?: ContentType;
  status?: ProcessingStatus;
  userId?: number;
  dateRange?: {
    from: string;
    to: string;
  };
  quality?: QualityLevel;
  searchTerm?: string;
}

export interface ContentSummary {
  totalContent: number;
  totalVideos: number;
  totalPhotos: number;
  completedContent: number;
  failedContent: number;
  processingContent: number;
  totalCreditsUsed: number;
  averageProcessingTime: number;
}

export interface BulkAction {
  action: 'delete' | 'retry' | 'cancel' | 'download';
  contentIds: string[];
  reason?: string;
}
