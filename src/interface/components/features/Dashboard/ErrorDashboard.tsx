/**
 * ErrorDashboard - Error state for dashboard
 */

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface ErrorDashboardProps {
  error: string;
  onRetry: () => void;
}

const ErrorDashboard: React.FC<ErrorDashboardProps> = ({
  error,
  onRetry
}) => {
  return (
    <div className="spacing-responsive">
      <Card className="p-8 text-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              Có lỗi xảy ra
            </h3>
            <p className="text-slate-600 mb-4">
              {error}
            </p>
          </div>
          
          <Button onClick={onRetry} className="flex items-center space-x-2">
            <RefreshCw className="w-4 h-4" />
            <span>Thử lại</span>
          </Button>
        </div>
      </Card>
    </div>
  );
};

export { ErrorDashboard };
