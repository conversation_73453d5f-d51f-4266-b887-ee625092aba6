/**
 * Credit Repository - Contains ALL credit-related business logic
 * Handles credit transactions, balance calculations, history tracking
 */

import { BaseRepository } from './baseRepository';
import { 
  CreditTransaction, 
  CreditBalance, 
  CreditUsage, 
  CreditSummary,
  CreditHistory,
  User,
  ApiResponse, 
  PaginatedResponse, 
  PaginationParams 
} from '@/models/types';
import { mockUsers } from '@/data/sources/mockDataSource';
import { 
  CREDIT_TRANSACTION_TYPES, 
  CREDIT_TRANSACTION_LABELS,
  TRANSACTION_STATUS,
  TRANSACTION_STATUS_LABELS,
  TRANSACTION_STATUS_COLORS 
} from '@/models/constants';

export class CreditRepository extends BaseRepository {
  private mockTransactions: CreditTransaction[] = [];

  constructor() {
    super();
    this.initializeMockTransactions();
  }

  /**
   * Initialize mock transaction data
   */
  private initializeMockTransactions(): void {
    mockUsers.forEach(user => {
      // Add initial credit transaction
      this.mockTransactions.push({
        id: this.generateId(),
        userId: user.id,
        type: 'add',
        amount: user.totalAdded,
        balance: user.credits,
        description: 'Credits ban đầu',
        timestamp: user.joinDate,
        status: 'completed'
      });

      // Add usage transactions
      if (user.totalUsed > 0) {
        this.mockTransactions.push({
          id: this.generateId(),
          userId: user.id,
          type: 'used',
          amount: user.totalUsed,
          balance: user.credits,
          description: 'Sử dụng tạo nội dung',
          timestamp: user.lastActivity,
          status: 'completed'
        });
      }
    });
  }

  /**
   * Add credits to user account
   */
  async addCredits(
    userId: number, 
    amount: number, 
    description: string = 'Thêm credits',
    adminId?: number,
    adminName?: string
  ): Promise<ApiResponse<CreditTransaction>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      if (amount <= 0) {
        throw new Error('Credit amount must be positive');
      }

      const userIndex = mockUsers.findIndex(u => u.id === userId);
      if (userIndex === -1) {
        throw new Error('User not found');
      }

      const user = mockUsers[userIndex];
      const newBalance = user.credits + amount;
      
      // Update user credits
      mockUsers[userIndex] = {
        ...user,
        credits: newBalance,
        totalAdded: user.totalAdded + amount,
        monthlyStats: {
          ...user.monthlyStats,
          creditsAdded: user.monthlyStats.creditsAdded + amount
        },
        lastActivity: new Date().toISOString()
      };

      // Create transaction record
      const transaction: CreditTransaction = {
        id: this.generateId(),
        userId,
        type: 'add',
        amount,
        balance: newBalance,
        description,
        timestamp: new Date().toISOString(),
        adminId,
        adminName,
        status: 'completed'
      };

      this.mockTransactions.push(transaction);
      return transaction;
    });
  }

  /**
   * Subtract credits from user account
   */
  async subtractCredits(
    userId: number, 
    amount: number, 
    description: string = 'Trừ credits',
    adminId?: number,
    adminName?: string
  ): Promise<ApiResponse<CreditTransaction>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      if (amount <= 0) {
        throw new Error('Credit amount must be positive');
      }

      const userIndex = mockUsers.findIndex(u => u.id === userId);
      if (userIndex === -1) {
        throw new Error('User not found');
      }

      const user = mockUsers[userIndex];
      
      if (user.credits < amount) {
        throw new Error('Insufficient credits');
      }

      const newBalance = user.credits - amount;
      
      // Update user credits
      mockUsers[userIndex] = {
        ...user,
        credits: newBalance,
        lastActivity: new Date().toISOString()
      };

      // Create transaction record
      const transaction: CreditTransaction = {
        id: this.generateId(),
        userId,
        type: 'subtract',
        amount,
        balance: newBalance,
        description,
        timestamp: new Date().toISOString(),
        adminId,
        adminName,
        status: 'completed'
      };

      this.mockTransactions.push(transaction);
      return transaction;
    });
  }

  /**
   * Use credits for content creation
   */
  async useCredits(
    userId: number,
    amount: number,
    contentType: 'video' | 'photo',
    contentId: string
  ): Promise<ApiResponse<CreditUsage>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const userIndex = mockUsers.findIndex(u => u.id === userId);
      if (userIndex === -1) {
        throw new Error('User not found');
      }

      const user = mockUsers[userIndex];
      
      if (user.credits < amount) {
        throw new Error('Insufficient credits');
      }

      const newBalance = user.credits - amount;
      
      // Update user credits and stats
      mockUsers[userIndex] = {
        ...user,
        credits: newBalance,
        totalUsed: user.totalUsed + amount,
        monthlyStats: {
          ...user.monthlyStats,
          creditsUsed: user.monthlyStats.creditsUsed + amount
        },
        lastActivity: new Date().toISOString()
      };

      // Create transaction record
      const transaction: CreditTransaction = {
        id: this.generateId(),
        userId,
        type: 'used',
        amount,
        balance: newBalance,
        description: `Tạo ${contentType === 'video' ? 'video' : 'ảnh'}`,
        timestamp: new Date().toISOString(),
        status: 'completed',
        metadata: { contentId }
      };

      this.mockTransactions.push(transaction);

      // Create usage record
      const usage: CreditUsage = {
        userId,
        contentType,
        creditsUsed: amount,
        contentId,
        timestamp: new Date().toISOString(),
        status: 'success'
      };

      return usage;
    });
  }

  /**
   * Get credit transaction history for user
   */
  async getCreditHistory(
    userId: number,
    params: PaginationParams
  ): Promise<ApiResponse<PaginatedResponse<CreditTransaction>>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const userTransactions = this.mockTransactions
        .filter(t => t.userId === userId)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      return this.formatPaginatedResponse(userTransactions, params);
    });
  }

  /**
   * Get credit balance for user
   */
  async getCreditBalance(userId: number): Promise<ApiResponse<CreditBalance>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const user = mockUsers.find(u => u.id === userId);
      if (!user) {
        throw new Error('User not found');
      }

      const balance: CreditBalance = {
        userId,
        currentBalance: user.credits,
        totalAdded: user.totalAdded,
        totalUsed: user.totalUsed,
        totalRefunded: 0, // TODO: implement refund tracking
        lastUpdated: user.lastActivity
      };

      return balance;
    });
  }

  /**
   * Get credit summary for dashboard
   */
  async getCreditSummary(): Promise<ApiResponse<CreditSummary>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const totalCreditsInSystem = mockUsers.reduce((sum, user) => sum + user.credits, 0);
      const totalCreditsIssued = mockUsers.reduce((sum, user) => sum + user.totalAdded, 0);
      const totalCreditsUsed = mockUsers.reduce((sum, user) => sum + user.totalUsed, 0);
      const totalCreditsRemaining = totalCreditsInSystem;
      const averageCreditsPerUser = totalCreditsInSystem / mockUsers.length;

      const topUsers = mockUsers
        .sort((a, b) => b.totalUsed - a.totalUsed)
        .slice(0, 5)
        .map(user => ({
          userId: user.id,
          userName: user.name,
          creditsUsed: user.totalUsed
        }));

      const summary: CreditSummary = {
        totalCreditsInSystem,
        totalCreditsIssued,
        totalCreditsUsed,
        totalCreditsRemaining,
        averageCreditsPerUser,
        topUsers
      };

      return summary;
    });
  }

  /**
   * Get transaction status display information
   */
  getTransactionStatusInfo(status: string) {
    return {
      label: TRANSACTION_STATUS_LABELS[status as keyof typeof TRANSACTION_STATUS_LABELS] || 'Không xác định',
      color: TRANSACTION_STATUS_COLORS[status as keyof typeof TRANSACTION_STATUS_COLORS] || 'bg-gray-100 text-gray-800'
    };
  }

  /**
   * Get transaction type display label
   */
  getTransactionTypeLabel(type: string): string {
    return CREDIT_TRANSACTION_LABELS[type as keyof typeof CREDIT_TRANSACTION_LABELS] || type;
  }
}

// Export singleton instance
export const creditRepository = new CreditRepository();
