/**
 * User Repository - Contains ALL user-related business logic
 * Replaces hardcoded data from UserCreditManagement.tsx
 */

import { BaseRepository } from './baseRepository';
import { 
  User, 
  UserFilters, 
  UserSummary, 
  UserActivityLog,
  ApiResponse, 
  PaginatedResponse, 
  PaginationParams 
} from '@/models/types';
import { mockUsers, getAggregatedUserStats } from '@/data/sources/mockDataSource';
import { USER_STATUS, USER_STATUS_LABELS, USER_STATUS_COLORS } from '@/models/constants';

export class UserRepository extends BaseRepository {
  
  /**
   * Get all users with filtering and pagination
   */
  async getUsers(
    params: PaginationParams,
    filters?: UserFilters
  ): Promise<ApiResponse<PaginatedResponse<User>>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      let filteredUsers = [...mockUsers];
      
      // Apply filters
      if (filters) {
        if (filters.status) {
          filteredUsers = filteredUsers.filter(user => user.status === filters.status);
        }
        
        if (filters.searchTerm) {
          const searchTerm = filters.searchTerm.toLowerCase();
          filteredUsers = filteredUsers.filter(user =>
            user.name.toLowerCase().includes(searchTerm) ||
            user.email.toLowerCase().includes(searchTerm) ||
            user.phone.includes(searchTerm)
          );
        }
        
        if (filters.dateRange) {
          filteredUsers = filteredUsers.filter(user => {
            const joinDate = new Date(user.joinDate);
            const fromDate = new Date(filters.dateRange!.from);
            const toDate = new Date(filters.dateRange!.to);
            return joinDate >= fromDate && joinDate <= toDate;
          });
        }
      }
      
      // Apply sorting
      const sortedUsers = this.applySorting(
        filteredUsers, 
        params.sortBy, 
        params.sortOrder
      );
      
      return this.formatPaginatedResponse(sortedUsers, params);
    });
  }

  /**
   * Get user by ID
   */
  async getUserById(id: number): Promise<ApiResponse<User | null>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const user = mockUsers.find(u => u.id === id);
      return user || null;
    });
  }

  /**
   * Create new user
   */
  async createUser(userData: Omit<User, 'id' | 'monthlyStats' | 'lastActivity' | 'joinDate'>): Promise<ApiResponse<User>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      // Validation
      this.validateRequired(userData, ['name', 'email', 'phone']);
      
      if (!this.isValidEmail(userData.email)) {
        throw new Error('Invalid email format');
      }
      
      if (!this.isValidPhone(userData.phone)) {
        throw new Error('Invalid phone format');
      }
      
      // Check if email already exists
      const existingUser = mockUsers.find(u => u.email === userData.email);
      if (existingUser) {
        throw new Error('Email already exists');
      }
      
      const newUser: User = {
        ...userData,
        id: Math.max(...mockUsers.map(u => u.id)) + 1,
        monthlyStats: {
          videosCompleted: 0,
          videosErrors: 0,
          photosCompleted: 0,
          photosErrors: 0,
          creditsUsed: 0,
          creditsAdded: 0
        },
        lastActivity: new Date().toISOString(),
        joinDate: new Date().toISOString().split('T')[0]
      };
      
      mockUsers.push(newUser);
      return newUser;
    });
  }

  /**
   * Update user
   */
  async updateUser(id: number, userData: Partial<User>): Promise<ApiResponse<User>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const userIndex = mockUsers.findIndex(u => u.id === id);
      if (userIndex === -1) {
        throw new Error('User not found');
      }
      
      // Validation for email if provided
      if (userData.email && !this.isValidEmail(userData.email)) {
        throw new Error('Invalid email format');
      }
      
      // Validation for phone if provided
      if (userData.phone && !this.isValidPhone(userData.phone)) {
        throw new Error('Invalid phone format');
      }
      
      // Check email uniqueness if email is being updated
      if (userData.email) {
        const existingUser = mockUsers.find(u => u.email === userData.email && u.id !== id);
        if (existingUser) {
          throw new Error('Email already exists');
        }
      }
      
      const updatedUser = {
        ...mockUsers[userIndex],
        ...userData,
        lastActivity: new Date().toISOString()
      };
      
      mockUsers[userIndex] = updatedUser;
      return updatedUser;
    });
  }

  /**
   * Delete user
   */
  async deleteUser(id: number): Promise<ApiResponse<boolean>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const userIndex = mockUsers.findIndex(u => u.id === id);
      if (userIndex === -1) {
        throw new Error('User not found');
      }
      
      mockUsers.splice(userIndex, 1);
      return true;
    });
  }

  /**
   * Get user summary statistics
   */
  async getUserSummary(): Promise<ApiResponse<UserSummary>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const stats = getAggregatedUserStats();
      
      return {
        totalUsers: stats.totalUsers,
        activeUsers: stats.activeUsers,
        premiumUsers: stats.premiumUsers,
        totalCredits: stats.totalCredits,
        totalCreditsAdded: stats.totalCreditsAdded,
        totalCreditsUsed: stats.totalCreditsUsed,
        totalVideos: stats.totalVideos,
        totalPhotos: stats.totalPhotos
      };
    });
  }

  /**
   * Calculate success rate for user (from existing component logic)
   */
  calculateSuccessRate(user: User): { video: number; photo: number } {
    const videoTotal = user.monthlyStats.videosCompleted + user.monthlyStats.videosErrors;
    const photoTotal = user.monthlyStats.photosCompleted + user.monthlyStats.photosErrors;
    
    return {
      video: videoTotal > 0 ? Math.round((user.monthlyStats.videosCompleted / videoTotal) * 100) : 0,
      photo: photoTotal > 0 ? Math.round((user.monthlyStats.photosCompleted / photoTotal) * 100) : 0
    };
  }

  /**
   * Get status display information
   */
  getStatusInfo(status: string) {
    return {
      label: USER_STATUS_LABELS[status as keyof typeof USER_STATUS_LABELS] || 'Không xác định',
      color: USER_STATUS_COLORS[status as keyof typeof USER_STATUS_COLORS] || 'bg-gray-100 text-gray-800'
    };
  }

  /**
   * Calculate monthly credit difference
   */
  calculateMonthlyCreditDifference(user: User): number {
    return user.monthlyStats.creditsAdded - user.monthlyStats.creditsUsed;
  }

  /**
   * Get user initials for avatar
   */
  getUserInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
  }
}

// Export singleton instance
export const userRepository = new UserRepository();
