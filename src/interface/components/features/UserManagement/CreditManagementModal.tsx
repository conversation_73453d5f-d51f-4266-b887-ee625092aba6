/**
 * CreditManagementModal - Modal for managing user credits
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Plus, Minus } from 'lucide-react';
import { ModalState } from '@/models/types';

interface CreditManagementModalProps {
  modal: ModalState;
  onClose: () => void;
  onAddCredits: (userId: number, amount: number, description: string) => Promise<void>;
  onSubtractCredits: (userId: number, amount: number, description: string) => Promise<void>;
  isLoading: boolean;
}

export const CreditManagementModal: React.FC<CreditManagementModalProps> = ({
  modal,
  onClose,
  onAddCredits,
  onSubtractCredits,
  isLoading
}) => {
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [operation, setOperation] = useState<'add' | 'subtract'>('add');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount || !description || !modal.data?.userId) {
      return;
    }

    const creditAmount = parseInt(amount);
    if (creditAmount <= 0) {
      return;
    }

    try {
      if (operation === 'add') {
        await onAddCredits(modal.data.userId, creditAmount, description);
      } else {
        await onSubtractCredits(modal.data.userId, creditAmount, description);
      }
      
      // Reset form
      setAmount('');
      setDescription('');
      setOperation('add');
    } catch (error) {
      console.error('Credit operation failed:', error);
    }
  };

  const handleClose = () => {
    setAmount('');
    setDescription('');
    setOperation('add');
    onClose();
  };

  return (
    <Dialog open={modal.isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{modal.title}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Operation Type */}
          <div className="space-y-2">
            <Label>Loại thao tác</Label>
            <div className="flex space-x-2">
              <Button
                type="button"
                variant={operation === 'add' ? 'default' : 'outline'}
                onClick={() => setOperation('add')}
                className="flex-1"
              >
                <Plus className="w-4 h-4 mr-2" />
                Thêm Credits
              </Button>
              <Button
                type="button"
                variant={operation === 'subtract' ? 'default' : 'outline'}
                onClick={() => setOperation('subtract')}
                className="flex-1"
              >
                <Minus className="w-4 h-4 mr-2" />
                Trừ Credits
              </Button>
            </div>
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">Số lượng Credits</Label>
            <Input
              id="amount"
              type="number"
              min="1"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="Nhập số lượng credits"
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Nhập mô tả cho thao tác này"
              rows={3}
              required
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !amount || !description}
              className={operation === 'add' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
            >
              {isLoading ? 'Đang xử lý...' : (operation === 'add' ? 'Thêm Credits' : 'Trừ Credits')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
