/**
 * UserStatsOverview - Stats overview component for user management
 */

import React from 'react';
import { Card } from '@/components/ui/card';
import { CreditCard, Plus, Minus, Video } from 'lucide-react';
import { UserSummary } from '@/models/types';
import { formatNumber } from '@/interface/utils/formatters';

interface UserStatsOverviewProps {
  summary?: UserSummary;
  isLoading: boolean;
}

export const UserStatsOverview: React.FC<UserStatsOverviewProps> = ({
  summary,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="grid-responsive-1-2-4 gap-4 sm:gap-6">
        {[...Array(4)].map((_, index) => (
          <Card key={index} className="p-4 sm:p-6 animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-200 rounded-lg"></div>
              <div className="min-w-0 flex-1">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-6 bg-gray-200 rounded"></div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (!summary) {
    return null;
  }

  const statsCards = [
    {
      title: 'Tổng Credits hiện tại',
      value: formatNumber(summary.totalCredits),
      icon: CreditCard,
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600'
    },
    {
      title: 'Credits đã thêm',
      value: formatNumber(summary.totalCreditsAdded),
      icon: Plus,
      iconBg: 'bg-green-100',
      iconColor: 'text-green-600'
    },
    {
      title: 'Credits đã sử dụng',
      value: formatNumber(summary.totalCreditsUsed),
      icon: Minus,
      iconBg: 'bg-red-100',
      iconColor: 'text-red-600'
    },
    {
      title: 'Tổng Videos',
      value: formatNumber(summary.totalVideos),
      icon: Video,
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-600'
    }
  ];

  return (
    <div className="grid-responsive-1-2-4 gap-4 sm:gap-6">
      {statsCards.map((stat, index) => (
        <Card key={index} className="p-4 sm:p-6">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 sm:w-12 sm:h-12 ${stat.iconBg} rounded-lg flex items-center justify-center`}>
              <stat.icon className={`w-5 h-5 sm:w-6 sm:h-6 ${stat.iconColor}`} />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-slate-600">{stat.title}</p>
              <p className="text-lg sm:text-2xl font-bold text-slate-900">
                {stat.value}
              </p>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};
