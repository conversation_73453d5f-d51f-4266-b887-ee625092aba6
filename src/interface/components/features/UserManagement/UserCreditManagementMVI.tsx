/**
 * UserCreditManagementMVI - Direct replacement for UserCreditManagement.tsx
 * Preserves EXACT same UI/UX but uses 3-layer MVI architecture
 * Fake data is now in repository layer instead of hardcoded in component
 */

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  UserPlus, 
  Mail, 
  Phone,
  CreditCard,
  Video,
  Image,
  Plus,
  Minus,
  Calendar,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Users,
  Activity
} from 'lucide-react';

// Data hooks - Repository integration (replaces hardcoded data)
import { useUsers, useUserHelpers, useAddCredits } from '@/interface/hooks/data';
import { usePagination } from '@/interface/hooks/ui';

const UserCreditManagementMVI = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [creditAmount, setCreditAmount] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedUserDetails, setSelectedUserDetails] = useState<number | null>(null);
  
  // Data from repository instead of hardcoded
  const pagination = usePagination({ initialPageSize: 100 }); // Get all users for now
  const { data: usersResponse, isLoading } = useUsers(
    pagination.paginationParams,
    { searchTerm: searchTerm || undefined }
  );
  const userHelpers = useUserHelpers();
  const addCreditsMutation = useAddCredits();
  
  // Extract users from response (same structure as before)
  const users = usersResponse?.items || [];
  
  // Filter users (same logic as before)
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Same event handlers as before
  const handleAddCredits = async (userId: number, amount: number) => {
    try {
      await addCreditsMutation.mutateAsync({
        userId,
        amount,
        description: `Thêm ${amount} credits bởi admin`,
        adminId: 1,
        adminName: 'Admin'
      });
      setSelectedUser(null);
      setCreditAmount('');
    } catch (error) {
      console.error('Failed to add credits:', error);
    }
  };

  const handleSubtractCredits = (userId: number, amount: number) => {
    console.log(`Subtracting ${amount} credits from user ${userId}`);
    // TODO: Implement subtract credits
  };

  // Same helper functions as before (now from repository)
  const getStatusColor = (status: string) => userHelpers.getStatusInfo(status).color;
  const getStatusText = (status: string) => userHelpers.getStatusInfo(status).label;

  // Same renderUserDetails function as before
  const renderUserDetails = (user: any) => {
    const successRate = userHelpers.calculateSuccessRate(user);

    return (
      <div className="mt-6 p-4 sm:p-6 bg-slate-50 rounded-lg">
        <h4 className="text-lg font-semibold mb-4">Chi tiết tháng này - {user.name}</h4>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Videos hoàn thành</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.videosCompleted}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Videos lỗi</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.videosErrors}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Image className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Photos hoàn thành</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.photosCompleted}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Photos lỗi</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.photosErrors}</p>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-4">
            <h5 className="font-semibold mb-3">Tỷ lệ thành công</h5>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm">Videos</span>
                  <span className="text-sm font-medium">{successRate.video}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full" 
                    style={{ width: `${successRate.video}%` }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm">Photos</span>
                  <span className="text-sm font-medium">{successRate.photo}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${successRate.photo}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <h5 className="font-semibold mb-3">Credits tháng này</h5>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Đã sử dụng</span>
                <span className="font-medium text-red-600">{user.monthlyStats.creditsUsed}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Đã thêm</span>
                <span className="font-medium text-green-600">{user.monthlyStats.creditsAdded}</span>
              </div>
              <div className="flex justify-between items-center pt-2 border-t">
                <span className="text-sm font-medium">Chênh lệch</span>
                <span className={`font-bold ${userHelpers.calculateMonthlyCreditDifference(user) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {userHelpers.calculateMonthlyCreditDifference(user) > 0 ? '+' : ''}{userHelpers.calculateMonthlyCreditDifference(user)}
                </span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="spacing-responsive">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  // EXACT same JSX as original UserCreditManagement.tsx
  return (
    <div className="spacing-responsive">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl font-bold text-slate-900">Quản lý người dùng & Credits</h2>
          <p className="text-sm sm:text-base text-slate-600">Quản lý người dùng, credits và thống kê chi tiết theo tháng</p>
        </div>
        <Button className="bg-indigo-600 hover:bg-indigo-700 flex-shrink-0">
          <UserPlus className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">Thêm người dùng</span>
          <span className="sm:hidden">Thêm</span>
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 border-b border-slate-200 overflow-x-auto">
        <button
          onClick={() => setActiveTab('overview')}
          className={`pb-2 px-1 border-b-2 transition-colors whitespace-nowrap ${
            activeTab === 'overview' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Tổng quan
        </button>
        <button
          onClick={() => setActiveTab('detailed')}
          className={`pb-2 px-1 border-b-2 transition-colors whitespace-nowrap ${
            activeTab === 'detailed' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Chi tiết theo tháng
        </button>
      </div>

      {activeTab === 'overview' && (
        <>
          {/* Stats Overview - Data now comes from repository */}
          <div className="grid-responsive-1-2-4 gap-4 sm:gap-6">
            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Tổng Credits hiện tại</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.credits, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Plus className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Credits đã thêm</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.totalAdded, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <Minus className="w-5 h-5 sm:w-6 sm:h-6 text-red-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Credits đã sử dụng</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.totalUsed, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Video className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Tổng Videos</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.videosCreated, 0)}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* User Management Table - EXACT same as original */}
          <Card className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  placeholder="Tìm kiếm theo tên hoặc email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex-shrink-0">
                <Filter className="w-4 h-4 mr-2" />
                Lọc
              </Button>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full min-w-[800px]">
                <thead>
                  <tr className="border-b border-slate-200">
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Người dùng</th>
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Credits</th>
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Nội dung</th>
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Trạng thái</th>
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="border-b border-slate-100 hover:bg-slate-50">
                      <td className="py-4 px-2 sm:px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-white font-medium text-xs sm:text-sm">
                              {userHelpers.getUserInitials(user.name)}
                            </span>
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="font-medium text-slate-900 truncate">{user.name}</p>
                            <div className="flex items-center text-xs sm:text-sm text-slate-500">
                              <Mail className="w-3 h-3 mr-1 flex-shrink-0" />
                              <span className="truncate">{user.email}</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-2 sm:px-4">
                        <Badge className="bg-blue-100 text-blue-800 text-xs">
                          {user.credits.toLocaleString()}
                        </Badge>
                      </td>
                      <td className="py-4 px-2 sm:px-4">
                        <div className="space-y-1">
                          <div className="flex items-center text-xs sm:text-sm">
                            <Video className="w-3 h-3 mr-1 text-blue-600 flex-shrink-0" />
                            <span>{user.videosCreated}</span>
                          </div>
                          <div className="flex items-center text-xs sm:text-sm">
                            <Image className="w-3 h-3 mr-1 text-green-600 flex-shrink-0" />
                            <span>{user.photosCreated}</span>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-2 sm:px-4">
                        <Badge className={getStatusColor(user.status)}>
                          {getStatusText(user.status)}
                        </Badge>
                      </td>
                      <td className="py-4 px-2 sm:px-4">
                        <div className="flex items-center space-x-2">
                          {selectedUser === user.id ? (
                            <div className="flex items-center space-x-2">
                              <Input
                                type="number"
                                placeholder="Credits"
                                value={creditAmount}
                                onChange={(e) => setCreditAmount(e.target.value)}
                                className="w-16 sm:w-20 text-xs"
                              />
                              <Button
                                size="sm"
                                onClick={() => {
                                  handleAddCredits(user.id, parseInt(creditAmount));
                                }}
                                className="bg-green-600 hover:bg-green-700 px-2"
                                disabled={addCreditsMutation.isPending}
                              >
                                <Plus className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedUser(null);
                                  setCreditAmount('');
                                }}
                                className="px-2"
                              >
                                Hủy
                              </Button>
                            </div>
                          ) : (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setSelectedUser(user.id)}
                                className="text-xs px-2"
                              >
                                <CreditCard className="w-3 h-3 mr-1" />
                                <span className="hidden sm:inline">Credits</span>
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => setSelectedUserDetails(selectedUserDetails === user.id ? null : user.id)}
                                className="px-2"
                              >
                                <BarChart3 className="w-3 h-3" />
                              </Button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* User Details */}
            {selectedUserDetails && (
              <>
                {filteredUsers.filter(user => user.id === selectedUserDetails).map(user => (
                  <div key={user.id}>
                    {renderUserDetails(user)}
                  </div>
                ))}
              </>
            )}
          </Card>
        </>
      )}

      {activeTab === 'detailed' && (
        <div className="spacing-responsive">
          <div className="grid-responsive-1-2-3 gap-4 sm:gap-6">
            {filteredUsers.map((user) => (
              <Card key={user.id} className="p-4 sm:p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-sm">
                      {userHelpers.getUserInitials(user.name)}
                    </span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-slate-900 truncate">{user.name}</h3>
                    <Badge className={getStatusColor(user.status)}>
                      {getStatusText(user.status)}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-slate-700 mb-2">Thống kê tháng này</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Videos hoàn thành</span>
                        <span className="font-medium text-green-600">{user.monthlyStats.videosCompleted}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Videos lỗi</span>
                        <span className="font-medium text-red-600">{user.monthlyStats.videosErrors}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Photos hoàn thành</span>
                        <span className="font-medium text-green-600">{user.monthlyStats.photosCompleted}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Photos lỗi</span>
                        <span className="font-medium text-red-600">{user.monthlyStats.photosErrors}</span>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex justify-between mb-2">
                      <span className="text-sm text-slate-600">Credits đã dùng</span>
                      <span className="font-medium text-red-600">{user.monthlyStats.creditsUsed}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-600">Credits đã thêm</span>
                      <span className="font-medium text-green-600">{user.monthlyStats.creditsAdded}</span>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default UserCreditManagementMVI;
