
import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Settings,
  CreditCard,
  Bell,
  Shield,
  Database,
  Mail,
  Palette,
  Globe,
  Save,
  RefreshCw
} from 'lucide-react';

const SystemSettings = () => {
  const [settings, setSettings] = useState({
    siteName: 'SaaS Video Creator',
    siteDescription: 'Platform tạo video và photo chuyên nghiệp',
    maintenanceMode: false,
    newUserRegistration: true,
    emailNotifications: true,
    maxCreditsPerUser: 1000,
    videoMaxDuration: 300,
    photoMaxSize: 10,
    defaultUserCredits: 100
  });

  const handleSave = () => {
    console.log('Saving settings:', settings);
    // Implement save logic here
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-slate-900"><PERSON><PERSON><PERSON> đặt hệ thống</h2>
        <p className="text-slate-600"><PERSON><PERSON><PERSON><PERSON> lý các cấu hình v<PERSON> thiết lập của platform</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <Card className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Settings className="w-5 h-5 text-indigo-600" />
            <h3 className="text-lg font-semibold text-slate-900">Cài đặt chung</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Tên trang web
              </label>
              <Input
                value={settings.siteName}
                onChange={(e) => setSettings({...settings, siteName: e.target.value})}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Mô tả trang web
              </label>
              <Input
                value={settings.siteDescription}
                onChange={(e) => setSettings({...settings, siteDescription: e.target.value})}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-700">Chế độ bảo trì</p>
                <p className="text-xs text-slate-500">Tạm dừng truy cập cho người dùng</p>
              </div>
              <Switch
                checked={settings.maintenanceMode}
                onCheckedChange={(checked) => setSettings({...settings, maintenanceMode: checked})}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-700">Đăng ký người dùng mới</p>
                <p className="text-xs text-slate-500">Cho phép người dùng mới đăng ký</p>
              </div>
              <Switch
                checked={settings.newUserRegistration}
                onCheckedChange={(checked) => setSettings({...settings, newUserRegistration: checked})}
              />
            </div>
          </div>
        </Card>

        {/* Credit Settings */}
        <Card className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <CreditCard className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-semibold text-slate-900">Cài đặt Credits</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Credits mặc định cho người dùng mới
              </label>
              <Input
                type="number"
                value={settings.defaultUserCredits}
                onChange={(e) => setSettings({...settings, defaultUserCredits: parseInt(e.target.value)})}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Giới hạn credits tối đa mỗi người dùng
              </label>
              <Input
                type="number"
                value={settings.maxCreditsPerUser}
                onChange={(e) => setSettings({...settings, maxCreditsPerUser: parseInt(e.target.value)})}
              />
            </div>
          </div>
        </Card>

        {/* Content Limits */}
        <Card className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Database className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-slate-900">Giới hạn nội dung</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Thời lượng video tối đa (giây)
              </label>
              <Input
                type="number"
                value={settings.videoMaxDuration}
                onChange={(e) => setSettings({...settings, videoMaxDuration: parseInt(e.target.value)})}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Kích thước photo tối đa (MB)
              </label>
              <Input
                type="number"
                value={settings.photoMaxSize}
                onChange={(e) => setSettings({...settings, photoMaxSize: parseInt(e.target.value)})}
              />
            </div>
          </div>
        </Card>

        {/* Notification Settings */}
        <Card className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Bell className="w-5 h-5 text-yellow-600" />
            <h3 className="text-lg font-semibold text-slate-900">Cài đặt thông báo</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-700">Email thông báo</p>
                <p className="text-xs text-slate-500">Gửi email thông báo cho admin</p>
              </div>
              <Switch
                checked={settings.emailNotifications}
                onCheckedChange={(checked) => setSettings({...settings, emailNotifications: checked})}
              />
            </div>
          </div>
        </Card>
      </div>

      {/* System Status */}
      <Card className="p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Shield className="w-5 h-5 text-purple-600" />
          <h3 className="text-lg font-semibold text-slate-900">Trạng thái hệ thống</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
            <div>
              <p className="text-sm font-medium text-green-800">Database</p>
              <p className="text-xs text-green-600">Hoạt động bình thường</p>
            </div>
            <Badge className="bg-green-100 text-green-800">Online</Badge>
          </div>

          <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
            <div>
              <p className="text-sm font-medium text-green-800">API Server</p>
              <p className="text-xs text-green-600">Phản hồi: 45ms</p>
            </div>
            <Badge className="bg-green-100 text-green-800">Healthy</Badge>
          </div>

          <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
            <div>
              <p className="text-sm font-medium text-blue-800">Storage</p>
              <p className="text-xs text-blue-600">Đã sử dụng: 78%</p>
            </div>
            <Badge className="bg-blue-100 text-blue-800">Normal</Badge>
          </div>
        </div>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end space-x-4">
        <Button variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Khôi phục mặc định
        </Button>
        <Button onClick={handleSave} className="bg-indigo-600 hover:bg-indigo-700">
          <Save className="w-4 h-4 mr-2" />
          Lưu cài đặt
        </Button>
      </div>
    </div>
  );
};

export default SystemSettings;
