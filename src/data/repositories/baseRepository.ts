/**
 * Base repository with shared logic for error handling, response formatting, and mock integration
 */

import { ApiResponse, PaginatedResponse, PaginationParams } from '@/models/types';

export class BaseRepository {
  protected async handleApiCall<T>(
    apiCall: () => Promise<T>,
    fallbackData?: T
  ): Promise<ApiResponse<T>> {
    try {
      const data = await apiCall();
      return {
        success: true,
        data,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('API call failed:', error);
      
      // Return fallback data if available (for mock mode)
      if (fallbackData !== undefined) {
        return {
          success: true,
          data: fallbackData,
          message: 'Using mock data',
          timestamp: new Date().toISOString()
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }

  protected async simulateApiDelay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  protected formatPaginatedResponse<T>(
    items: T[],
    params: PaginationParams,
    totalCount?: number
  ): PaginatedResponse<T> {
    const { page, pageSize } = params;
    const total = totalCount ?? items.length;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      items: paginatedItems,
      totalCount: total,
      pageSize,
      currentPage: page,
      totalPages: Math.ceil(total / pageSize),
      hasNextPage: page * pageSize < total,
      hasPreviousPage: page > 1
    };
  }

  protected applyFilters<T>(
    items: T[],
    filters: Record<string, any>
  ): T[] {
    return items.filter(item => {
      return Object.entries(filters).every(([key, value]) => {
        if (value === undefined || value === null || value === '') {
          return true;
        }

        const itemValue = (item as any)[key];
        
        if (typeof value === 'string' && typeof itemValue === 'string') {
          return itemValue.toLowerCase().includes(value.toLowerCase());
        }
        
        if (Array.isArray(value)) {
          return value.includes(itemValue);
        }
        
        return itemValue === value;
      });
    });
  }

  protected applySorting<T>(
    items: T[],
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'asc'
  ): T[] {
    if (!sortBy) return items;

    return [...items].sort((a, b) => {
      const aValue = (a as any)[sortBy];
      const bValue = (b as any)[sortBy];

      let comparison = 0;
      
      if (aValue < bValue) {
        comparison = -1;
      } else if (aValue > bValue) {
        comparison = 1;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }

  protected generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  protected formatError(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'An unknown error occurred';
  }

  protected validateRequired(data: Record<string, any>, requiredFields: string[]): void {
    const missingFields = requiredFields.filter(field => 
      data[field] === undefined || data[field] === null || data[field] === ''
    );

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }
  }

  protected isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  protected isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s-()]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
  }
}
