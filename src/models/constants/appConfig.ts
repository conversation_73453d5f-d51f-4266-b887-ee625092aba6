/**
 * Application configuration constants
 */

export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  USERS_PAGE_SIZE: 20,
  CONTENT_PAGE_SIZE: 15,
  TRANSACTIONS_PAGE_SIZE: 25
} as const;

export const REFRESH_INTERVALS = {
  DASHBOARD_STATS: 30000, // 30 seconds
  USER_ACTIVITY: 60000,   // 1 minute
  SYSTEM_HEALTH: 120000,  // 2 minutes
  NOTIFICATIONS: 300000   // 5 minutes
} as const;

export const CACHE_KEYS = {
  DASHBOARD_STATS: 'dashboard_stats',
  USER_LIST: 'user_list',
  CONTENT_LIST: 'content_list',
  SYSTEM_SETTINGS: 'system_settings'
} as const;

export const CACHE_DURATIONS = {
  SHORT: 5 * 60 * 1000,    // 5 minutes
  MEDIUM: 15 * 60 * 1000,  // 15 minutes
  LONG: 60 * 60 * 1000,    // 1 hour
  VERY_LONG: 24 * 60 * 60 * 1000 // 24 hours
} as const;

export const UI_CONSTANTS = {
  SIDEBAR_WIDTH: 280,
  HEADER_HEIGHT: 64,
  MOBILE_BREAKPOINT: 768,
  TABLET_BREAKPOINT: 1024,
  DESKTOP_BREAKPOINT: 1280
} as const;

export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500
} as const;

export const TOAST_DURATIONS = {
  SUCCESS: 3000,
  ERROR: 5000,
  WARNING: 4000,
  INFO: 3000
} as const;

export const VALIDATION_RULES = {
  MIN_PASSWORD_LENGTH: 8,
  MAX_NAME_LENGTH: 100,
  MAX_EMAIL_LENGTH: 255,
  MAX_PHONE_LENGTH: 20,
  MIN_CREDITS: 0,
  MAX_CREDITS: 999999
} as const;
