/**
 * Analytics Repository - Contains ALL analytics and dashboard statistics logic
 * Replaces hardcoded statsData from Dashboard.tsx
 */

import { BaseRepository } from './baseRepository';
import { 
  StatsData, 
  QuickStats, 
  AnalyticsMetrics,
  DashboardStats,
  TrendData,
  GrowthChart,
  PerformanceMetrics,
  ReportData,
  ApiResponse 
} from '@/models/types';
import { 
  mockStatsData, 
  mockQuickStats, 
  mockAnalyticsMetrics,
  mockUsers 
} from '@/data/sources/mockDataSource';

export class AnalyticsRepository extends BaseRepository {

  /**
   * Get dashboard statistics - replaces Dashboard.tsx statsData
   */
  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      // Calculate real-time stats from mock data
      const totalUsers = mockUsers.length;
      const activeUsers = mockUsers.filter(u => u.status === 'active').length;
      const totalCredits = mockUsers.reduce((sum, user) => sum + user.credits, 0);
      const totalCreditsUsed = mockUsers.reduce((sum, user) => sum + user.totalUsed, 0);
      const totalVideos = mockUsers.reduce((sum, user) => sum + user.videosCreated, 0);
      const totalPhotos = mockUsers.reduce((sum, user) => sum + user.photosCreated, 0);

      // Update stats with real data
      const updatedStatsData = mockStatsData.map(stat => {
        switch (stat.title) {
          case 'Tổng người dùng cá nhân':
            return {
              ...stat,
              value: totalUsers.toLocaleString(),
              subtitle: `${activeUsers} đang hoạt động`
            };
          case 'Credits cá nhân đã dùng':
            return {
              ...stat,
              value: totalCreditsUsed.toLocaleString(),
              subtitle: `${Math.floor(totalCreditsUsed * 0.02)} hôm nay`
            };
          case 'Videos cá nhân':
            return {
              ...stat,
              value: totalVideos.toLocaleString(),
              subtitle: `${Math.floor(totalVideos * 0.04)} hôm nay`
            };
          default:
            return stat;
        }
      });

      // Update quick stats with real data
      const updatedQuickStats = mockQuickStats.map(stat => {
        switch (stat.label) {
          case 'Credits còn lại hệ thống':
            return {
              ...stat,
              value: this.formatNumber(totalCredits)
            };
          case 'Người dùng mới hôm nay':
            return {
              ...stat,
              value: Math.floor(totalUsers * 0.001).toString()
            };
          case 'Credits đã phát hành':
            const totalIssued = mockUsers.reduce((sum, user) => sum + user.totalAdded, 0);
            return {
              ...stat,
              value: this.formatNumber(totalIssued)
            };
          default:
            return stat;
        }
      });

      return {
        totalUsers: updatedStatsData[0],
        creditsUsed: updatedStatsData[1],
        videosCreated: updatedStatsData[2],
        quickStats: updatedQuickStats
      };
    });
  }

  /**
   * Get analytics metrics
   */
  async getAnalyticsMetrics(period: string = 'month'): Promise<ApiResponse<AnalyticsMetrics>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const totalUsers = mockUsers.length;
      const activeUsers = mockUsers.filter(u => u.status === 'active').length;
      const totalCredits = mockUsers.reduce((sum, user) => sum + user.credits, 0);
      const totalCreditsUsed = mockUsers.reduce((sum, user) => sum + user.totalUsed, 0);
      const totalCreditsIssued = mockUsers.reduce((sum, user) => sum + user.totalAdded, 0);
      const totalVideos = mockUsers.reduce((sum, user) => sum + user.videosCreated, 0);
      const totalPhotos = mockUsers.reduce((sum, user) => sum + user.photosCreated, 0);

      const metrics: AnalyticsMetrics = {
        totalUsers,
        activeUsers,
        newUsersToday: Math.floor(totalUsers * 0.001),
        totalCredits,
        creditsUsedToday: Math.floor(totalCreditsUsed * 0.02),
        creditsRemaining: totalCredits,
        creditsIssued: totalCreditsIssued,
        totalVideos,
        videosToday: Math.floor(totalVideos * 0.04),
        totalPhotos,
        photosToday: Math.floor(totalPhotos * 0.03)
      };

      return metrics;
    });
  }

  /**
   * Get user growth chart data
   */
  async getUserGrowthChart(period: string = 'month'): Promise<ApiResponse<GrowthChart>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      // Generate mock growth data
      const labels = this.generateDateLabels(period);
      const userData = this.generateGrowthData(labels.length, mockUsers.length);
      const creditData = this.generateGrowthData(labels.length, mockUsers.reduce((sum, u) => sum + u.totalUsed, 0));

      const chart: GrowthChart = {
        labels,
        datasets: [
          {
            label: 'Người dùng',
            data: userData,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4
          },
          {
            label: 'Credits sử dụng',
            data: creditData,
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4
          }
        ]
      };

      return chart;
    });
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(): Promise<ApiResponse<PerformanceMetrics>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const totalVideoCompleted = mockUsers.reduce((sum, u) => sum + u.monthlyStats.videosCompleted, 0);
      const totalVideoErrors = mockUsers.reduce((sum, u) => sum + u.monthlyStats.videosErrors, 0);
      const totalPhotoCompleted = mockUsers.reduce((sum, u) => sum + u.monthlyStats.photosCompleted, 0);
      const totalPhotoErrors = mockUsers.reduce((sum, u) => sum + u.monthlyStats.photosErrors, 0);

      const videoTotal = totalVideoCompleted + totalVideoErrors;
      const photoTotal = totalPhotoCompleted + totalPhotoErrors;

      const metrics: PerformanceMetrics = {
        successRate: {
          video: videoTotal > 0 ? Math.round((totalVideoCompleted / videoTotal) * 100) : 0,
          photo: photoTotal > 0 ? Math.round((totalPhotoCompleted / photoTotal) * 100) : 0
        },
        averageProcessingTime: {
          video: 45, // seconds
          photo: 15  // seconds
        },
        errorRate: {
          video: videoTotal > 0 ? Math.round((totalVideoErrors / videoTotal) * 100) : 0,
          photo: photoTotal > 0 ? Math.round((totalPhotoErrors / photoTotal) * 100) : 0
        }
      };

      return metrics;
    });
  }

  /**
   * Generate report data
   */
  async generateReport(type: 'daily' | 'weekly' | 'monthly' | 'yearly'): Promise<ApiResponse<ReportData>> {
    return this.handleApiCall(async () => {
      await this.simulateApiDelay();
      
      const metrics = await this.getAnalyticsMetrics();
      const growthChart = await this.getUserGrowthChart(type);

      const report: ReportData = {
        id: this.generateId(),
        title: `Báo cáo ${type === 'daily' ? 'hàng ngày' : type === 'weekly' ? 'hàng tuần' : type === 'monthly' ? 'hàng tháng' : 'hàng năm'}`,
        type,
        generatedAt: new Date().toISOString(),
        data: metrics.data!,
        charts: [growthChart.data!]
      };

      return report;
    });
  }

  /**
   * Helper: Format large numbers
   */
  private formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  /**
   * Helper: Generate date labels for charts
   */
  private generateDateLabels(period: string): string[] {
    const labels: string[] = [];
    const now = new Date();
    
    switch (period) {
      case 'week':
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);
          labels.push(date.toLocaleDateString('vi-VN', { weekday: 'short' }));
        }
        break;
      case 'month':
        for (let i = 29; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);
          labels.push(date.toLocaleDateString('vi-VN', { day: 'numeric', month: 'short' }));
        }
        break;
      case 'year':
        for (let i = 11; i >= 0; i--) {
          const date = new Date(now);
          date.setMonth(date.getMonth() - i);
          labels.push(date.toLocaleDateString('vi-VN', { month: 'short', year: 'numeric' }));
        }
        break;
      default:
        labels.push('Hôm nay');
    }
    
    return labels;
  }

  /**
   * Helper: Generate growth data for charts
   */
  private generateGrowthData(length: number, maxValue: number): number[] {
    const data: number[] = [];
    let currentValue = Math.floor(maxValue * 0.7);
    
    for (let i = 0; i < length; i++) {
      const variation = (Math.random() - 0.5) * 0.1 * currentValue;
      currentValue = Math.max(0, currentValue + variation);
      data.push(Math.floor(currentValue));
    }
    
    // Ensure the last value is close to maxValue
    data[data.length - 1] = maxValue;
    
    return data;
  }
}

// Export singleton instance
export const analyticsRepository = new AnalyticsRepository();
