/**
 * UserManagementContainer - New component using 3-layer MVI architecture
 * Replaces UserCreditManagement.tsx with proper separation of concerns
 */

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserPlus } from 'lucide-react';

// Data hooks - Repository integration
import { 
  useUsers, 
  useUserSummary, 
  useUserHelpers,
  useAddCredits,
  useSubtractCredits
} from '@/interface/hooks/data';

// UI hooks - State management
import { usePagination, useFilters, useModal } from '@/interface/hooks/ui';

// Validation
import { validateUserFilters, validatePagination } from '@/interface/utils/validation';

// Components


// Types
import { UserFilters } from '@/models/types';
import { UserDetailsModal } from './UserDetailsModal';
import { CreditManagementModal } from './CreditManagementModal';
import { UserStatsOverview } from './UserStatsOverview';
import { UserTable } from './UserTable';

const UserManagementContainer: React.FC = () => {
  // UI State
  const [activeTab, setActiveTab] = useState<'overview' | 'detailed'>('overview');
  const [selectedUserDetails, setSelectedUserDetails] = useState<number | null>(null);
  
  // Pagination
  const pagination = usePagination({
    initialPageSize: 20
  });
  
  // Filters
  const filters = useFilters<UserFilters>({
    initialFilters: {}
  });
  
  // Modals
  const userModal = useModal();
  const creditModal = useModal();
  
  // Data hooks
  const { 
    data: usersData, 
    isLoading: isLoadingUsers, 
    error: usersError 
  } = useUsers(
    pagination.paginationParams,
    filters.filters
  );
  
  const { 
    data: userSummary, 
    isLoading: isLoadingSummary 
  } = useUserSummary();
  
  // Helper functions
  const userHelpers = useUserHelpers();
  
  // Credit mutations
  const addCreditsMutation = useAddCredits();
  const subtractCreditsMutation = useSubtractCredits();
  
  // Event handlers
  const handleAddCredits = async (userId: number, amount: number, description: string) => {
    try {
      await addCreditsMutation.mutateAsync({
        userId,
        amount,
        description,
        adminId: 1, // TODO: Get from auth context
        adminName: 'Admin' // TODO: Get from auth context
      });
      creditModal.closeModal();
    } catch (error) {
      console.error('Failed to add credits:', error);
    }
  };
  
  const handleSubtractCredits = async (userId: number, amount: number, description: string) => {
    try {
      await subtractCreditsMutation.mutateAsync({
        userId,
        amount,
        description,
        adminId: 1, // TODO: Get from auth context
        adminName: 'Admin' // TODO: Get from auth context
      });
      creditModal.closeModal();
    } catch (error) {
      console.error('Failed to subtract credits:', error);
    }
  };
  
  const handleUserDetailsToggle = (userId: number) => {
    setSelectedUserDetails(selectedUserDetails === userId ? null : userId);
  };
  
  const handleCreateUser = () => {
    userModal.openModal({
      title: 'Thêm người dùng mới',
      size: 'lg'
    });
  };
  
  const handleManageCredits = (userId: number, userName: string) => {
    creditModal.openModal({
      title: `Quản lý Credits - ${userName}`,
      size: 'md',
      data: { userId, userName }
    });
  };

  if (usersError) {
    return (
      <div className="spacing-responsive">
        <Card className="p-6 text-center">
          <p className="text-red-600">Có lỗi xảy ra khi tải dữ liệu người dùng</p>
          <Button 
            onClick={() => window.location.reload()} 
            className="mt-4"
          >
            Thử lại
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="spacing-responsive">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl font-bold text-slate-900">
            Quản lý người dùng & Credits
          </h2>
          <p className="text-sm sm:text-base text-slate-600">
            Quản lý người dùng, credits và thống kê chi tiết theo tháng
          </p>
        </div>
        <Button 
          onClick={handleCreateUser}
          className="bg-indigo-600 hover:bg-indigo-700 flex-shrink-0"
        >
          <UserPlus className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">Thêm người dùng</span>
          <span className="sm:hidden">Thêm</span>
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 border-b border-slate-200 overflow-x-auto">
        <button
          onClick={() => setActiveTab('overview')}
          className={`pb-2 px-1 border-b-2 transition-colors whitespace-nowrap ${
            activeTab === 'overview' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Tổng quan
        </button>
        <button
          onClick={() => setActiveTab('detailed')}
          className={`pb-2 px-1 border-b-2 transition-colors whitespace-nowrap ${
            activeTab === 'detailed' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Chi tiết theo tháng
        </button>
      </div>

      {/* Content */}
      {activeTab === 'overview' && (
        <>
          {/* Stats Overview */}
          <UserStatsOverview 
            summary={userSummary}
            isLoading={isLoadingSummary}
          />

          {/* User Table */}
          <UserTable
            users={usersData?.items || []}
            isLoading={isLoadingUsers}
            pagination={pagination}
            filters={filters}
            selectedUserDetails={selectedUserDetails}
            onUserDetailsToggle={handleUserDetailsToggle}
            onManageCredits={handleManageCredits}
            userHelpers={userHelpers}
          />
        </>
      )}

      {activeTab === 'detailed' && (
        <div className="spacing-responsive">
          <div className="grid-responsive-1-2-3 gap-4 sm:gap-6">
            {usersData?.items.map((user) => (
              <Card key={user.id} className="p-4 sm:p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-sm">
                      {userHelpers.getUserInitials(user.name)}
                    </span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-slate-900 truncate">{user.name}</h3>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${userHelpers.getStatusInfo(user.status).color}`}>
                      {userHelpers.getStatusInfo(user.status).label}
                    </span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-slate-700 mb-2">Thống kê tháng này</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Videos hoàn thành</span>
                        <span className="font-medium text-green-600">{user.monthlyStats.videosCompleted}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Videos lỗi</span>
                        <span className="font-medium text-red-600">{user.monthlyStats.videosErrors}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Photos hoàn thành</span>
                        <span className="font-medium text-green-600">{user.monthlyStats.photosCompleted}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Photos lỗi</span>
                        <span className="font-medium text-red-600">{user.monthlyStats.photosErrors}</span>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex justify-between mb-2">
                      <span className="text-sm text-slate-600">Credits đã dùng</span>
                      <span className="font-medium text-red-600">{user.monthlyStats.creditsUsed}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-600">Credits đã thêm</span>
                      <span className="font-medium text-green-600">{user.monthlyStats.creditsAdded}</span>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Modals */}
      <UserDetailsModal 
        modal={userModal}
        onClose={userModal.closeModal}
      />
      
      <CreditManagementModal
        modal={creditModal}
        onClose={creditModal.closeModal}
        onAddCredits={handleAddCredits}
        onSubtractCredits={handleSubtractCredits}
        isLoading={addCreditsMutation.isPending || subtractCreditsMutation.isPending}
      />
    </div>
  );
};

export default UserManagementContainer;
