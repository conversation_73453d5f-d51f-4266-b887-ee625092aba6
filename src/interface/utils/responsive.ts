/**
 * Responsive utilities và breakpoint helpers
 */

import { UI_CONSTANTS } from '@/models/constants';

// Breakpoint definitions
export const BREAKPOINTS = {
  mobile: UI_CONSTANTS.MOBILE_BREAKPOINT,
  tablet: UI_CONSTANTS.TABLET_BREAKPOINT,
  desktop: UI_CONSTANTS.DESKTOP_BREAKPOINT
} as const;

// Media query strings
export const MEDIA_QUERIES = {
  mobile: `(max-width: ${BREAKPOINTS.mobile - 1}px)`,
  tablet: `(min-width: ${BREAKPOINTS.mobile}px) and (max-width: ${BREAKPOINTS.tablet - 1}px)`,
  desktop: `(min-width: ${BREAKPOINTS.tablet}px)`,
  largeDesktop: `(min-width: ${BREAKPOINTS.desktop}px)`
} as const;

// Hook for detecting screen size
export const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      
      if (width < BREAKPOINTS.mobile) {
        setScreenSize('mobile');
      } else if (width < BREAKPOINTS.tablet) {
        setScreenSize('tablet');
      } else {
        setScreenSize('desktop');
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return screenSize;
};

// Hook for media query matching
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
};

// Responsive helper functions
export const isMobile = (): boolean => {
  return window.innerWidth < BREAKPOINTS.mobile;
};

export const isTablet = (): boolean => {
  return window.innerWidth >= BREAKPOINTS.mobile && window.innerWidth < BREAKPOINTS.tablet;
};

export const isDesktop = (): boolean => {
  return window.innerWidth >= BREAKPOINTS.tablet;
};

// Responsive class utilities
export const getResponsiveClasses = (
  mobile: string,
  tablet?: string,
  desktop?: string
): string => {
  const classes = [mobile];
  
  if (tablet) {
    classes.push(`md:${tablet}`);
  }
  
  if (desktop) {
    classes.push(`lg:${desktop}`);
  }
  
  return classes.join(' ');
};

// Grid responsive classes
export const GRID_CLASSES = {
  'grid-responsive-1-2-3': 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  'grid-responsive-1-2-4': 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  'grid-responsive-1-3': 'grid grid-cols-1 lg:grid-cols-3',
  'grid-responsive-2-4': 'grid grid-cols-2 lg:grid-cols-4'
} as const;

// Spacing responsive classes
export const SPACING_CLASSES = {
  'spacing-responsive': 'space-y-4 md:space-y-6 lg:space-y-8',
  'padding-responsive': 'p-4 md:p-6 lg:p-8',
  'margin-responsive': 'm-4 md:m-6 lg:m-8'
} as const;

// Text responsive classes
export const TEXT_CLASSES = {
  'text-responsive-sm': 'text-sm md:text-base',
  'text-responsive-base': 'text-base md:text-lg',
  'text-responsive-lg': 'text-lg md:text-xl lg:text-2xl',
  'text-responsive-xl': 'text-xl md:text-2xl lg:text-3xl'
} as const;

// Import React hooks
import { useState, useEffect } from 'react';
