/**
 * QuickStatsRow - Quick stats component
 */

import React from 'react';
import { Card } from '@/components/ui/card';
import { QuickStats } from '@/models/types';

interface QuickStatsRowProps {
  quickStats: QuickStats[];
  isLoading: boolean;
}

export const QuickStatsRow: React.FC<QuickStatsRowProps> = ({
  quickStats,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="grid-responsive-1-2-3 gap-4">
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="p-4 sm:p-6 text-center animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-6 bg-gray-200 rounded"></div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid-responsive-1-2-3 gap-4">
      {quickStats.map((stat, index) => (
        <Card key={index} className="p-4 sm:p-6 text-center">
          <p className="text-xs sm:text-sm text-slate-600 mb-1">{stat.label}</p>
          <p className={`text-lg sm:text-xl font-bold ${stat.color}`}>{stat.value}</p>
        </Card>
      ))}
    </div>
  );
};
