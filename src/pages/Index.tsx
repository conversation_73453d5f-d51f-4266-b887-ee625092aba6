
import { useState } from 'react';
import Login from '@/components/Login';
import Dashboard from '@/components/Dashboard';

// Import new MVI components for testing
import DashboardMVI from '@/interface/components/features/Dashboard/DashboardMVI';

const Index = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [useMVI, setUseMVI] = useState(false); // Toggle between old and new architecture

  const handleLogin = () => {
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  return (
    <>
      {isLoggedIn ? (
        <>
          {/* Toggle button for testing */}
          <div className="fixed top-4 right-4 z-50">
            <button
              onClick={() => setUseMVI(!useMVI)}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm"
            >
              {useMVI ? 'Old UI' : 'MVI UI'}
            </button>
          </div>

          {/* Render based on architecture choice */}
          {useMVI ? (
            <DashboardMVI onLogout={handleLogout} />
          ) : (
            <Dashboard onLogout={handleLogout} />
          )}
        </>
      ) : (
        <Login onLogin={handleLogin} />
      )}
    </>
  );
};

export default Index;
