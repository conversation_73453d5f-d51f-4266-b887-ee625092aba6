/**
 * useModal Hook - Modal state management cho dialogs
 */

import { useState, useCallback } from 'react';
import { ModalState } from '@/models/types';

export const useModal = (initialState: Partial<ModalState> = {}) => {
  const [modal, setModal] = useState<ModalState>({
    isOpen: false,
    title: '',
    size: 'md',
    data: null,
    ...initialState
  });

  const openModal = useCallback((options: Partial<ModalState> = {}) => {
    setModal(prev => ({
      ...prev,
      isOpen: true,
      ...options
    }));
  }, []);

  const closeModal = useCallback(() => {
    setModal(prev => ({
      ...prev,
      isOpen: false
    }));
  }, []);

  const updateModal = useCallback((updates: Partial<ModalState>) => {
    setModal(prev => ({
      ...prev,
      ...updates
    }));
  }, []);

  return {
    modal,
    openModal,
    closeModal,
    updateModal,
    isOpen: modal.isOpen
  };
};
