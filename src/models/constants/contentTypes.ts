/**
 * Content management constants
 */

export const CONTENT_TYPES = {
  VIDEO: 'video',
  PHOTO: 'photo'
} as const;

export const CONTENT_TYPE_LABELS = {
  [CONTENT_TYPES.VIDEO]: 'Video',
  [CONTENT_TYPES.PHOTO]: 'Ảnh'
} as const;

export const PROCESSING_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
} as const;

export const PROCESSING_STATUS_LABELS = {
  [PROCESSING_STATUS.PENDING]: 'Chờ xử lý',
  [PROCESSING_STATUS.PROCESSING]: '<PERSON>ang xử lý',
  [PROCESSING_STATUS.COMPLETED]: 'Hoàn thành',
  [PROCESSING_STATUS.FAILED]: 'Thất bại',
  [PROCESSING_STATUS.CANCELLED]: 'Đã hủy'
} as const;

export const PROCESSING_STATUS_COLORS = {
  [PROCESSING_STATUS.PENDING]: 'bg-yellow-100 text-yellow-800',
  [PROCESSING_STATUS.PROCESSING]: 'bg-blue-100 text-blue-800',
  [PROCESSING_STATUS.COMPLETED]: 'bg-green-100 text-green-800',
  [PROCESSING_STATUS.FAILED]: 'bg-red-100 text-red-800',
  [PROCESSING_STATUS.CANCELLED]: 'bg-gray-100 text-gray-800'
} as const;

export const QUALITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  ULTRA: 'ultra'
} as const;

export const QUALITY_LEVEL_LABELS = {
  [QUALITY_LEVELS.LOW]: 'Thấp',
  [QUALITY_LEVELS.MEDIUM]: 'Trung bình',
  [QUALITY_LEVELS.HIGH]: 'Cao',
  [QUALITY_LEVELS.ULTRA]: 'Siêu cao'
} as const;

export const MIME_TYPES = {
  VIDEO: {
    MP4: 'video/mp4',
    WEBM: 'video/webm',
    AVI: 'video/avi'
  },
  IMAGE: {
    JPEG: 'image/jpeg',
    PNG: 'image/png',
    WEBP: 'image/webp',
    GIF: 'image/gif'
  }
} as const;

export const FILE_SIZE_LIMITS = {
  VIDEO_MAX: 500 * 1024 * 1024, // 500MB
  IMAGE_MAX: 50 * 1024 * 1024,  // 50MB
  THUMBNAIL_MAX: 5 * 1024 * 1024 // 5MB
} as const;
